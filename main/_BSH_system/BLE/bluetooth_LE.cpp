#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "esp_err.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

#include "esp_bt_main.h"
#include "esp_bt.h"
#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_bt_defs.h"
#include "esp_gatt_common_api.h"
#include "esp_bt_device.h"
#include "esp_timer.h"

#include "bluetooth_LE.h"
#include "ble_internal.h"
#include "DEVICE_BLE_CONFIG.h"
#include "system.h"
#include "rsa_encryption.h"
#include "commissioning.h"



namespace bsh_sys::ble {



ESP_EVENT_DEFINE_BASE (BLE_EVENTS);



/* ======= local defines for constants =========*/
#define OUT_BUFF_SIZE 250
#define MTU_SIZE 360

#define GATTS_RX_VAL_LEN_MAX 200
#define GATTS_TX_VAL_LEN_MAX 30 
#define GATTS_VER_VAL_LEN_MAX 22
#define GATTS_STATUS_VAL_LEN_MAX BLE_SATUS_CHAR_ARR_SIZE
#define GATTS_TELEM_VAL_LEN_MAX 350 // matches mqtt max block size - to send tlm via ble
#define GATTS_TERMINAL_OUT_VAL_LEN_MAX BLE_LOGGING_OUT_BUF_SIZE 
#define GATTS_TERMINAL_IN_VAL_LEN_MAX BLE_LOGGING_IN_BUF_SIZE 

#define PREPARE_BUF_MAX_SIZE 1024 

#define PROFILE_NUM 1
#define PROFILE_CONTROL 0
#define MAX_CHARS_IN_SERV 5

static const char* TAG = "-ble-";



/* ======= local object declarations =========*/
static bsh_sys::ble::ble_settings_t *settings = NULL;
static bool connected = false;
static TaskHandle_t ble_task_handle;
static uint8_t *to_send_buff = NULL;
static bool init_done = false;
static bool accept_connection = false;   // shows if device accepts connections
static bool allow_conn_when_init_done = true;
static esp_timer_handle_t connect_timer = NULL;
static uint8_t adv_config_done = 0;
#define adv_config_flag      (1 << 0)
#define scan_rsp_config_flag (1 << 1)

static uint8_t device_name[22] = {0};
static uint8_t fw_version[GATTS_VER_VAL_LEN_MAX]= {0};
static uint8_t rx_value_arr[GATTS_RX_VAL_LEN_MAX] = {0};
static uint8_t tx_value_arr[GATTS_TX_VAL_LEN_MAX] = {0};
static uint8_t status_value_arr[GATTS_STATUS_VAL_LEN_MAX] = {0};
static uint8_t telemetry_value_arr[GATTS_TELEM_VAL_LEN_MAX] = {0};
static uint8_t terminal_out_arr[GATTS_TERMINAL_OUT_VAL_LEN_MAX] = {0};
static uint8_t terminal_in_arr[GATTS_TERMINAL_IN_VAL_LEN_MAX] = {0};
static uint8_t dev_type[4]= {};   // its a value. not same as device name which is string like Q781
static bool shut_down_ble_when_timer_elapses = false;
static void (* ble_down_callback)(void) = NULL;

static bool logs_sent = true;
static bool terminal_subscription = false;

// The length of adv data must be less than 31 bytes
static uint8_t manufacturer_data[] = {0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}; 
// 6 bytes used to share mac addr. see fill_man_data()
// byte N 3 filled in with device code after ble init. code provided externally



static esp_ble_adv_params_t adv_params = {
    .adv_int_min        = 0x20,
    .adv_int_max        = 0x40,
    .adv_type           = ADV_TYPE_IND,
    .own_addr_type      = BLE_ADDR_TYPE_PUBLIC,
    .peer_addr            = {},
    .peer_addr_type       = {},
    .channel_map        = ADV_CHNL_ALL,
    .adv_filter_policy = ADV_FILTER_ALLOW_SCAN_ANY_CON_ANY,
};

typedef struct
{
    uint16_t char_handle;
    esp_bt_uuid_t char_uuid;
    esp_gatt_perm_t perm;
    esp_gatt_char_prop_t property;
    uint16_t descr_handle;
    esp_bt_uuid_t descr_uuid;
} char_str;

struct gatts_profile_inst {
    esp_gatts_cb_t gatts_cb;
    uint16_t gatts_if;
    uint16_t app_id;
    uint16_t conn_id;
    uint16_t service_handle;
    esp_gatt_srvc_id_t service_id;
    char_str service_char[MAX_CHARS_IN_SERV];
};

/* One gatt-based profile one app_id and one gatts_if, this array will store the gatts_if returned by ESP_GATTS_REG_EVT */
static void gatts_profile_control_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param);
static struct gatts_profile_inst gl_profile_tab[PROFILE_NUM] = {
    [PROFILE_CONTROL] = {
        .gatts_cb = gatts_profile_control_event_handler,
        .gatts_if = ESP_GATT_IF_NONE,       /* Not get the gatt_if, so initial is ESP_GATT_IF_NONE */
    },
};

static uint8_t adv_service_uuid128[16] = {
    /* LSB <--------------------------------------------------------> MSB */
    // if need to add more than one UUID, just stack them one after another in this array
    0xfb, 0x34, 0x9b, 0x5f, 0x80, 0x00, 0x00, 0x80, 0x00, 0x10, 0x00, 0x00, 0xF3, 0x00, 0x00, 0x00,
};

// static uint8_t adv_service_uuid128[32] = {
//     /* LSB <--------------------------------------------------------------------------------> MSB */
//     //first uuid, 16bit, [12],[13] is the value
//     0xfb, 0x34, 0x9b, 0x5f, 0x80, 0x00, 0x00, 0x80, 0x00, 0x10, 0x00, 0x00, 0xDE, 0x00, 0x00, 0x00,
//     //second uuid, 32bit, [12], [13], [14], [15] is the value
//     0xfb, 0x34, 0x9b, 0x5f, 0x80, 0x00, 0x00, 0x80, 0x00, 0x10, 0x00, 0x00, 0xF2, 0x00, 0x00, 0x00,
// };


//adv data
static esp_ble_adv_data_t adv_data = {
    .set_scan_rsp = false,
    .include_name = true,
    .include_txpower = false,
    .min_interval = 0x0006, //slave connection min interval, Time = min_interval * 1.25 msec
    .max_interval = 0x0010, //slave connection max interval, Time = max_interval * 1.25 msec
    .appearance = 0x00,
    .manufacturer_len = sizeof(manufacturer_data),
    .p_manufacturer_data =  manufacturer_data,
    .service_data_len = 0,
    .p_service_data = NULL,
    .service_uuid_len = 0,
    .p_service_uuid = NULL,
    .flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT),
};
// scan response data
static esp_ble_adv_data_t scan_rsp_data = {
    .set_scan_rsp = true,
    .include_name = false,
    .include_txpower = true,
    //.min_interval = 0x0006,
    //.max_interval = 0x0010,
    .appearance = 0x00,
	.manufacturer_len = 0,
	.p_manufacturer_data =  NULL,
    .service_data_len = 0,
    .p_service_data = NULL,
    .service_uuid_len = sizeof(adv_service_uuid128),
    .p_service_uuid = adv_service_uuid128,
    // .flag = (ESP_BLE_ADV_FLAG_GEN_DISC | ESP_BLE_ADV_FLAG_BREDR_NOT_SPT),
};

typedef struct {
    uint8_t *prepare_buf;
    int prepare_len;
} prepare_type_env_t;

static prepare_type_env_t a_prepare_write_env;



enum
{
    BORK_CONTROL_SERVICE,

    CHAR_DEV_TYPE,
    CHAR_DEV_TYPE_VALUE,
    
    CHAR_FIRMVARE_VER,
    CHAR_FIRMVARE_VER_VALUE,

    CHAR_RX,
    CHAR_RX_VALUE,

    CHAR_TX,
    CHAR_TX_VALUE,
    CHAR_TX_CFG,

    CHAR_STATUS,
    CHAR_STATUS_VALUE,
    CHAR_STATUS_CFG,

    CHAR_TELEM,
    CHAR_TELEM_VALUE,
    CHAR_TELEM_CFG,

    CHAR_TERM_OUT,
    CHAR_TERM_OUT_VALUE,
    CHAR_TERM_OUT_CFG,

    CHAR_TERM_IN,
    CHAR_TERM_IN_VALUE,

    ATTRIBUTES_QTY,
};

uint16_t handle_table[ATTRIBUTES_QTY];

static const uint16_t primary_service_uuid         = ESP_GATT_UUID_PRI_SERVICE;
static const uint16_t GATTS_BORK_SERVICE_UUID      = GATTS_SERVICE_UUID_CONTROL;
static const uint16_t GATTS_CHAR_UUID_DEV_TYPE     = GATTS_CHAR_DEVICE_TYPE;
static const uint16_t GATTS_CHAR_UUID_FIRM_VER     = GATTS_CHAR_FIRM_VER;
static const uint16_t GATTS_CHAR_UUID_RX           = GATTS_CHAR_RX;
static const uint16_t GATTS_CHAR_UUID_TX           = GATTS_CHAR_TX;
static const uint16_t GATTS_CHAR_UUID_STATUS       = GATTS_CHAR_STATUS;
static const uint16_t GATTS_CHAR_UUID_TELEM        = GATTS_CHAR_TELEM;
static const uint16_t GATTS_CHAR_UUID_TERM_OUT     = GATTS_CHAR_TERM_OUT;
static const uint16_t GATTS_CHAR_UUID_TERM_IN      = GATTS_CHAR_TERM_IN;

static const uint16_t character_declaration_uuid   = ESP_GATT_UUID_CHAR_DECLARE;
static const uint16_t character_client_config_uuid = ESP_GATT_UUID_CHAR_CLIENT_CONFIG;
static const uint8_t char_prop_read                = ESP_GATT_CHAR_PROP_BIT_READ;
static const uint8_t char_prop_write               = ESP_GATT_CHAR_PROP_BIT_WRITE;
static const uint8_t char_prop_read_write_notify   = ESP_GATT_CHAR_PROP_BIT_WRITE | ESP_GATT_CHAR_PROP_BIT_READ | ESP_GATT_CHAR_PROP_BIT_NOTIFY;

static const uint8_t ccc_descriptor[2]      = {0x00, 0x00};

#define CHAR_DECLARATION_SIZE       (sizeof(uint8_t))



static const esp_gatts_attr_db_t gatt_db[ATTRIBUTES_QTY] =
{
    // Service Declaration
    [BORK_CONTROL_SERVICE]        =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&primary_service_uuid,     
                            ESP_GATT_PERM_READ,
                            sizeof(uint16_t), 
                            sizeof(GATTS_BORK_SERVICE_UUID),     
                            (uint8_t *)&GATTS_BORK_SERVICE_UUID,
                        }},

    /* Device type       Characteristic Declaration */
    [CHAR_DEV_TYPE]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_READ,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_read}},

    /* Characteristic Value */
    [CHAR_DEV_TYPE_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_DEV_TYPE, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            20, 
                            4,    //size of device_type
                            (uint8_t *)dev_type}},  
    


    /* Firmware version.      Characteristic Declaration */
    [CHAR_FIRMVARE_VER]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_READ,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_read}},

    /* Characteristic Value */
    [CHAR_FIRMVARE_VER_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_FIRM_VER, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            22, 
                            20,   
                            (uint8_t *)fw_version}},
    

    
    /* RX.          Characteristic Declaration */
    [CHAR_RX]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_WRITE,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_write}},

    /* Characteristic Value */
    [CHAR_RX_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_RX, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            GATTS_RX_VAL_LEN_MAX, 
                            sizeof(rx_value_arr),   
                            (uint8_t *)rx_value_arr}},



    /* TX.          Characteristic Declaration */
    [CHAR_TX]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_READ,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_read_write_notify}},

    /* Characteristic Value */
    [CHAR_TX_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_TX, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            GATTS_TX_VAL_LEN_MAX, 
                            sizeof(tx_value_arr),   
                            (uint8_t *)tx_value_arr}},

    /* Client Characteristic Configuration Descriptor */
    [CHAR_TX_CFG]  =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_client_config_uuid, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            sizeof(uint16_t), 
                            sizeof(ccc_descriptor), 
                            (uint8_t *)ccc_descriptor}},



    /* Status          Characteristic Declaration */
    [CHAR_STATUS]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_READ,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_read_write_notify}},

    /* Characteristic Value */
    [CHAR_STATUS_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_STATUS, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            50, 
                            sizeof(status_value_arr),   
                            (uint8_t *)status_value_arr}},

    /* Client Characteristic Configuration Descriptor */
    [CHAR_STATUS_CFG]  =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_client_config_uuid, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            sizeof(uint16_t), 
                            sizeof(ccc_descriptor), 
                            (uint8_t *)ccc_descriptor}},


    /* Telemetry          Characteristic Declaration */
    [CHAR_TELEM]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_READ,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_read_write_notify}},

    /* Characteristic Value */
    [CHAR_TELEM_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_TELEM, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            GATTS_TELEM_VAL_LEN_MAX, 
                            sizeof(telemetry_value_arr),   
                            (uint8_t *)telemetry_value_arr}},

    /* Client Characteristic Configuration Descriptor */
    [CHAR_TELEM_CFG]  =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_client_config_uuid, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            sizeof(uint16_t), 
                            sizeof(ccc_descriptor), 
                            (uint8_t *)ccc_descriptor}},



    /* Terminal OUT          Characteristic Declaration */
    [CHAR_TERM_OUT]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_READ,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_read_write_notify}},

    /* Characteristic Value */
    [CHAR_TERM_OUT_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_TERM_OUT, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            GATTS_TERMINAL_OUT_VAL_LEN_MAX, 
                            sizeof(terminal_out_arr),   
                            (uint8_t *)terminal_out_arr}},

    /* Client Characteristic Configuration Descriptor */
    [CHAR_TERM_OUT_CFG]  =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_client_config_uuid, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            sizeof(uint16_t), 
                            sizeof(ccc_descriptor),   
                            (uint8_t *)ccc_descriptor}},


    
    /* Terminal input Characteristic Declaration */
    [CHAR_TERM_IN]     =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&character_declaration_uuid, 
                            ESP_GATT_PERM_WRITE,
                            CHAR_DECLARATION_SIZE, 
                            CHAR_DECLARATION_SIZE, 
                            (uint8_t *)&char_prop_write}},

    /* Characteristic Value */
    [CHAR_TERM_IN_VALUE] =
    {{ESP_GATT_AUTO_RSP}, {ESP_UUID_LEN_16, 
                            (uint8_t *)&GATTS_CHAR_UUID_TERM_IN, 
                            ESP_GATT_PERM_READ | ESP_GATT_PERM_WRITE,
                            GATTS_TERMINAL_IN_VAL_LEN_MAX, 
                            sizeof(terminal_in_arr),   
                            (uint8_t *)terminal_in_arr}},
};



/* ======= local function declarations ========= */
static bool conn_settings_check(bsh_sys::ble::ble_settings_t *setting_to_check);
static void ble_task(void *);
static void init();
static void fill_man_data();
static void set_security();

static bool init_connect_timer();
static bool start_connect_timer(uint32_t time_s);
static void stop_connect_timer();
static void connect_timer_handler(void *);

static void enable_adv ();
static void disable_adv ();

static void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param);
static void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param);
static void write_event_env(esp_gatt_if_t gatts_if, prepare_type_env_t *prepare_write_env, esp_ble_gatts_cb_param_t *param);

// this function treats incoming data
static void got_ble_packet(uint8_t *data, uint16_t length);

static void ble_adapter_init(esp_gatts_cb_t gatts_event_hldr, esp_gap_ble_cb_t gap_event_hdlr, uint16_t app_id);
static const char *esp_auth_req_to_str(esp_ble_auth_req_t auth_req);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
esp_err_t start(ble_settings_t *setts)
{
    ESP_LOGW (TAG,"Starting BLE");
    if (!conn_settings_check(setts)) 
    {
        ESP_LOGW (TAG,"start failed");
        return ESP_ERR_INVALID_ARG ;
    }
    settings = setts;

    // set firmware version
    int i = 0;
    fw_version[i]= 'v';   i++;
    if (settings->firware_version_major > 0xff) { fw_version[i] = (uint8_t)((settings->firware_version_major>>8) + 0x30); i++; }
    fw_version[i] = (uint8_t)(settings->firware_version_major + 0x30); i++;
    fw_version[i] = '.'; i++;

    if (settings->firmware_version_minor > 0xff) { fw_version[i] = (uint8_t)((settings->firmware_version_minor>>8) + 0x30); i++; }
    fw_version[i] = (uint8_t)((settings->firmware_version_minor) + 0x30); i++;
    fw_version[i] = '.'; i++;

    if (settings->firmware_version_patch > 0xff) { fw_version[i] = (uint8_t)((settings->firmware_version_patch>>8) + 0x30); i++; }
    fw_version[i] = (uint8_t)((settings->firmware_version_patch) + 0x30); i++;
    fw_version[i] = '+'; i++;

    memcpy(&fw_version[i], settings->commit_hash,7);


    memcpy(dev_type, settings->device_type, 4);
    memcpy(device_name, settings->device_name, strlen(settings->device_name));

    esp_err_t ret;
    ret = xTaskCreate(ble_task, "bleTask", 4096, NULL, 12, &ble_task_handle);
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to initialize ble task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }
    ESP_LOGW (TAG,"BLE task created");

    return ESP_OK;
}



void stop()
{
    ESP_LOGW (TAG,"Stopping ble");
    // esp_ble_gap_stop_advertising();   // not advertising anyway
    // esp_ble_gap_stop_scanning();      // not scanning at the moment
    // esp_bluedroid_disable();          // stuck if called: never returns.. 
    // esp_bluedroid_deinit();              // need to disable before calling deinit..
    esp_bt_controller_disable();
    esp_bt_controller_deinit();
    esp_bt_controller_mem_release(ESP_BT_MODE_BLE);
    ESP_LOGW (TAG,"Ble stopped");
    // esp_bt_controller_mem_release(ESP_BT_MODE_BTDM);    // crashed if trying to free BTDM memory
}



void allow_connections()
{
    ESP_LOGW (TAG,"allowing connections");
    if (!init_done) 
    {
        allow_conn_when_init_done = true; // this function will be called one more time, after adv params set = init completed
        return;
    }
 
    disconnect();
 
    if (!bsh_sys::ble_internal::is_first_time()) start_connect_timer(CONNECTION_TIME);
    accept_connection = true;

    enable_adv();
}



void stop_connections()
{
    disconnect();
    stop_connect_timer();
    accept_connection = false;
    disable_adv();
}



void stop_connections_after_while(uint16_t time_seconds)
{
    ESP_LOGI (TAG,"Connections will be off after %i", time_seconds);
    start_connect_timer((uint32_t)time_seconds);
}



void stop_connections_after_while_and_shut_down_ble(uint16_t time_seconds,  void (* ble_down_cb)(void))
{
    ESP_LOGI (TAG,"Ble will be off after %i", time_seconds);
    start_connect_timer((uint32_t)time_seconds);
    shut_down_ble_when_timer_elapses = true;
    ble_down_callback = ble_down_cb;
}



bool are_connections_allowed ()
{
    return accept_connection;
}



bool is_connected()
{
    return connected;
}



bool send_via_bt(const uint8_t *data, uint16_t length)
{
   	int32_t ret;
    if (!connected) 
    {
        return false;
    }

    if (data == NULL)
    {
        ESP_LOGE (TAG, "null ptr");
        return false;
    }

    ESP_LOGI (TAG,"sending via BLE:  %i bytes", length);
	ESP_LOG_BUFFER_HEXDUMP(TAG, (void *)data, length, ESP_LOG_INFO);
	memcpy(tx_value_arr, data, length);
	ret = esp_ble_gatts_send_indicate( gl_profile_tab[PROFILE_CONTROL].gatts_if, gl_profile_tab[PROFILE_CONTROL].conn_id, handle_table[CHAR_TX_VALUE], length, tx_value_arr, false);
	// ESP_LOGI("BT"," sent.  ret %d, con_id %d", ret, gl_profile_tab[PROFILE_CONTROL].conn_id);
	if(ret == ESP_OK) 
    {
        ESP_LOGI (TAG,"  sent");
        return true;
    } else {
        ESP_LOGE (TAG,"err sending via ble");
    }

	return false;
}



bool sent_telemetry_via_bt(const uint8_t *data, uint16_t length)
{
    int32_t ret;
    if (!connected) 
    {
        return false;
    }

    if (data == NULL)
    {
        ESP_LOGE (TAG, "null ptr");
        return false;
    }

    ESP_LOGI (TAG,"sending T via BLE");
    ESP_LOG_BUFFER_HEXDUMP(TAG, (void *)data, length, ESP_LOG_INFO);
	memcpy(telemetry_value_arr, data, length);
	ret = esp_ble_gatts_send_indicate( gl_profile_tab[PROFILE_CONTROL].gatts_if, gl_profile_tab[PROFILE_CONTROL].conn_id, handle_table[CHAR_TELEM_VALUE], length, telemetry_value_arr, false);
	// ESP_LOGI("BT"," sent.  ret %d, con_id %d", ret, gl_profile_tab[PROFILE_CONTROL].conn_id);
	if(ret == ESP_OK) 
    {
        ESP_LOGI (TAG,"sent:");
        return true;
    } else {
        ESP_LOGE (TAG,"err sending via ble");
    }

	return false;
}



void send_to_logging_char(const char * message)
{
    if (!connected) 
    {
        return;
    }

    if (message == NULL)
    {
        ESP_LOGE (TAG, "null ptr");
        return;
    }

    if(!terminal_subscription) return;

    int length = strlen(message);

    // ESP_LOGI (TAG, "sending logs to ble. %i bytes", length);
    // ESP_LOG_BUFFER_HEXDUMP(TAG, (void *)message, length+10, ESP_LOG_INFO);
    memcpy(terminal_out_arr, message, length + 1);

    logs_sent = false;
    int ret = esp_ble_gatts_send_indicate( gl_profile_tab[PROFILE_CONTROL].gatts_if, gl_profile_tab[PROFILE_CONTROL].conn_id, handle_table[CHAR_TERM_OUT_VALUE], length, terminal_out_arr, false);
	if(ret == ESP_OK) 
    {
        // ESP_LOGI (TAG,"sent");
        return;
    } else {
        ESP_LOGE (TAG,"err sending logs");
        logs_sent = true;
    }
}



bool update_status_data(const uint8_t *data, uint8_t length)
{
    int32_t ret;
    if (!connected) 
    {
        return false;
    }

    if (data == NULL)
    {
        ESP_LOGE (TAG, "null ptr");
        return false;
    }

    if (length > GATTS_STATUS_VAL_LEN_MAX) 
    {
        length = GATTS_STATUS_VAL_LEN_MAX;
        ESP_LOGW (TAG,"status char data truncated. length: %u", length);
    }

    // ESP_LOGI (TAG,"updating status char: %02x %02x %02x %02x",data[0], data[1], data[2], data[3]);
	// ESP_LOG_BUFFER_HEXDUMP(TAG, (void *)data, lenth, ESP_LOG_INFO);

	memcpy(status_value_arr, data, length);
	ret = esp_ble_gatts_send_indicate( gl_profile_tab[PROFILE_CONTROL].gatts_if, gl_profile_tab[PROFILE_CONTROL].conn_id, handle_table[CHAR_STATUS_VALUE], length, status_value_arr, false);
	// ESP_LOGI("BT"," sent.  ret %d, con_id %d", ret, gl_profile_tab[PROFILE_CONTROL].conn_id);
	if(ret == ESP_OK) return true;
	return false;
}



void disconnect (){
    ESP_LOGW(TAG,"disconnected");
    if (gl_profile_tab[PROFILE_CONTROL].gatts_if != ESP_GATT_IF_NONE){
        esp_ble_gatts_close(
                gl_profile_tab[PROFILE_CONTROL].gatts_if,
                gl_profile_tab[PROFILE_CONTROL].conn_id); 
    }
}



void change_device_creds (const char *name, const char *type, uint8_t code)
{
    ESP_LOGW(TAG, "changing BLE name to %s   and type to %s   and code to %i", name, type, code);
    
    bool adv_was_enabled = are_connections_allowed();
    if (adv_was_enabled) disable_adv();

    settings->device_code = code;
    memcpy(settings->device_type, type, 4);
    memset(device_name, 0, sizeof(device_name));
    memcpy(device_name, name, strlen(name)+1);
    esp_ble_gap_set_device_name((const char*)device_name);
    esp_ble_gap_config_adv_data(&adv_data);
    if (adv_was_enabled) enable_adv();
}



const char * get_device_name()
{
    return (const char *)device_name;
}



const char * ble_event_to_string(ble_events_t event)
{
    switch (event)
    {
    case BLE_CONNECTED: return "ble connected";
    case BLE_PAIRING: return "ble pairing";
    case BLE_PAIRING_STOPPED_BY_TIMEOUT: return "ble stopped by timeout";
    case BLE_DISCONNECTED: return "ble disconnected";
    }
    return "";
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static void ble_adapter_init(esp_gatts_cb_t gatts_event_hldr, esp_gap_ble_cb_t gap_event_hdlr, uint16_t app_id)
{
    ESP_LOGW (TAG,"Adapter init");
        esp_err_t ret;
    ESP_ERROR_CHECK(esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT));
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret) {
        ESP_LOGE(TAG, "%s initialize controller failed: %s\n", __func__, esp_err_to_name(ret));
        return;
    }
    ESP_LOGI (TAG,"    init");
    // TODO switch controller to BLE only, and disable ble client to save memory?
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret) {
        ESP_LOGE(TAG, "%s enable controller failed: %s\n", __func__, esp_err_to_name(ret));
        return;
    }
    ESP_LOGI (TAG,"    enable");
    ret = esp_bluedroid_init();
    if (ret) {
        ESP_LOGE(TAG, "%s init bluetooth failed: %s\n", __func__, esp_err_to_name(ret));
        return;
    }

    ESP_LOGI (TAG,"    bluedroid init");
    ret = esp_bluedroid_enable();
    if (ret) {
        ESP_LOGE(TAG, "%s enable bluetooth failed: %s\n", __func__, esp_err_to_name(ret));
        return;
    }

    fill_man_data();  // call only after esp_bluedroid_enable !!!
    
    ESP_LOGI (TAG,"    bluedroid enable");
    ret = esp_ble_gatts_register_callback(gatts_event_hldr);
    if (ret){
        ESP_LOGE(TAG, "gatts register error, error code = %x", ret);
        return;
    }
    
    ESP_LOGI (TAG,"    reg gatts cb");
    ret = esp_ble_gap_register_callback(gap_event_hdlr);
    if (ret){
        ESP_LOGE(TAG, "gap register error, error code = %x", ret);
        return;
    }

    ESP_LOGI (TAG,"    reg gap cb");
    ret = esp_ble_gatts_app_register(app_id);
    
    if (ret)
    {
        ESP_LOGE(TAG, "gatts app register error, error code = %x", ret);
        return;
    }
    ESP_LOGI (TAG,"    reg app");
    ESP_LOGW (TAG,"Adapter init done");
}



static bool conn_settings_check(bsh_sys::ble::ble_settings_t *setting_to_check)
{
    if(setting_to_check->sending_queue == NULL || 
        setting_to_check->receiving_queue == NULL || 
        setting_to_check->incoming_packet_check == NULL ||
        setting_to_check->device_name == NULL ||
        setting_to_check->ble_command_executor == NULL)
    {
        ESP_LOGE (TAG,"null ptr in init parameters");
        return false;
    }
    return true;
}



static void ble_task(void *)
{
    if (!init_done) init();

    for(;;)
    {
        xQueueReceive( *settings->sending_queue, to_send_buff, portMAX_DELAY);
        send_via_bt(((ble_queue_obj_t*)to_send_buff)->data, ((ble_queue_obj_t*)to_send_buff)->data_len);
        vTaskDelay (pdMS_TO_TICKS(10));
    }
}



static void init()
{
    ESP_LOGW (TAG,"Ble init..");

    if (to_send_buff != NULL) free (to_send_buff);
    to_send_buff = (uint8_t *)malloc (OUT_BUFF_SIZE);
    
    if (to_send_buff == NULL)
    {
        ESP_LOGE(TAG, "Failed to malloc ble buffer, restarting device...");
        esp_restart();
    }

    ble_adapter_init(gatts_event_handler, gap_event_handler, PROFILE_CONTROL);

    esp_err_t local_mtu_ret = esp_ble_gatt_set_local_mtu(MTU_SIZE);
    if (local_mtu_ret){
        ESP_LOGE(TAG, "set local  MTU failed, error code = %x", local_mtu_ret);
    }

    set_security();
    
    allow_connections();  // connections will be disallowed after two minutes

    ESP_LOGW(TAG, "Ble init done");
    bsh_sys::rsa::init();

    init_done = true;
}



// Call only after "esp_bluedroid_enable" coz gets ble mac addr.
static void fill_man_data()
{
    manufacturer_data[0] = DEVICE_MANUFACTURER_CODE;
    manufacturer_data[2] = settings->device_code;
    memcpy (manufacturer_data + 6, esp_bt_dev_get_address(), 6);

    ESP_LOGI (TAG,"advertising manufacturer data:");
	ESP_LOG_BUFFER_HEXDUMP(TAG, manufacturer_data, 12, ESP_LOG_INFO);
}



static void set_security()
{
	    esp_ble_auth_req_t auth_req = ESP_LE_AUTH_NO_BOND;     //bonding with peer device after authentication
	    esp_ble_io_cap_t iocap = ESP_IO_CAP_NONE;           //set the IO capability to No output No input
	    uint8_t key_size = 16;      //the key size should be 7~16 bytes
	    uint8_t init_key = ESP_BLE_ENC_KEY_MASK | ESP_BLE_ID_KEY_MASK | ESP_BLE_CSR_KEY_MASK | ESP_BLE_LINK_KEY_MASK;
	    uint8_t rsp_key = ESP_BLE_ENC_KEY_MASK | ESP_BLE_ID_KEY_MASK | ESP_BLE_CSR_KEY_MASK | ESP_BLE_LINK_KEY_MASK;
	    //set static passkey
	    uint32_t passkey = 123456;
	    uint8_t auth_option = ESP_BLE_ONLY_ACCEPT_SPECIFIED_AUTH_DISABLE;
	    // uint8_t oob_support = ESP_BLE_OOB_ENABLE;
	    esp_ble_gap_set_security_param(ESP_BLE_SM_SET_STATIC_PASSKEY, &passkey, sizeof(uint32_t));
	    esp_ble_gap_set_security_param(ESP_BLE_SM_AUTHEN_REQ_MODE, &auth_req, sizeof(uint8_t));
	    esp_ble_gap_set_security_param(ESP_BLE_SM_IOCAP_MODE, &iocap, sizeof(uint8_t));
	    esp_ble_gap_set_security_param(ESP_BLE_SM_MAX_KEY_SIZE, &key_size, sizeof(uint8_t));
	    esp_ble_gap_set_security_param(ESP_BLE_SM_ONLY_ACCEPT_SPECIFIED_SEC_AUTH, &auth_option, sizeof(uint8_t));
	    // esp_ble_gap_set_security_param(ESP_BLE_SM_OOB_SUPPORT, &oob_support, sizeof(uint8_t));
	    /* If your BLE device acts as a Slave, the init_key means you hope which types of key of the master should distribute to you,
	    and the response key means which key you can distribute to the master;
	    If your BLE device acts as a master, the response key means you hope which types of key of the slave should distribute to you,
	    and the init key means which key you can distribute to the slave. */
	    esp_ble_gap_set_security_param(ESP_BLE_SM_SET_INIT_KEY, &init_key, sizeof(uint8_t));
	    esp_ble_gap_set_security_param(ESP_BLE_SM_SET_RSP_KEY, &rsp_key, sizeof(uint8_t));
}



static bool init_connect_timer()
{
	const esp_timer_create_args_t connect_timer_args = {connect_timer_handler, 0, {}, 0, 0};

	int ret = 0;
	if((ret = esp_timer_create(&connect_timer_args, &connect_timer)) == ESP_OK)
		return true;

	ESP_LOGE(TAG,"cant create connect timer %d", ret);
	return false;
}

static bool start_connect_timer(uint32_t time_s)
{

    if (connect_timer == NULL) init_connect_timer();

	stop_connect_timer();
	int ret = 0;
	if((ret = esp_timer_start_once(connect_timer, time_s*1000*1000)) == ESP_OK)
	{
        ESP_LOGI (TAG,"connection timer started");
		return true;
	}
	ESP_LOGE(TAG,"cant start connect timer %d", ret);

	return false;
}

static void stop_connect_timer()
{
    esp_timer_stop(connect_timer);
    ESP_LOGI (TAG,"connection timer stopped");
}

static void connect_timer_handler(void *)
{
	esp_event_post_to(settings->loop_handle, BLE_EVENTS, BLE_PAIRING_STOPPED_BY_TIMEOUT, NULL, 0, 0);
    accept_connection = false;
    disable_adv();

    if(shut_down_ble_when_timer_elapses)
    {
        stop();
        if(ble_down_callback != NULL) ble_down_callback();
    }
}



static void enable_adv ()
{
    ESP_LOGI (TAG,"advertisement on");
    esp_event_post_to(settings->loop_handle, BLE_EVENTS, BLE_PAIRING, NULL, 0, 0);
    esp_ble_gap_start_advertising(&adv_params);
    bsh_sys::ble_internal::up_time_start_clock();
}

static void disable_adv ()
{
    ESP_LOGI (TAG,"advertisement off");
    esp_ble_gap_stop_advertising();
    bsh_sys::ble_internal::up_time_stop_clock();
}



static void gatts_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    if (event == ESP_GATTS_REG_EVT) {
        /* If event is register event, store the gatts_if for each profile */
        if (param->reg.status == ESP_GATT_OK) {
            gl_profile_tab[param->reg.app_id].gatts_if = gatts_if;
        } else {
            ESP_LOGE(TAG, "Reg app failed, app_id %04x, status %d\n",
                    param->reg.app_id,
                    param->reg.status);
            return;
        }
    }

    /* If the gatts_if equal to profile A, call profile A cb handler,
    * so here call each profile's callback */
    do {
        int idx;
        for (idx = 0; idx < PROFILE_NUM; idx++) {
            if (gatts_if == ESP_GATT_IF_NONE || /* ESP_GATT_IF_NONE, not specify a certain gatt_if, need to call every profile cb function */
                    gatts_if == gl_profile_tab[idx].gatts_if) {
                if (gl_profile_tab[idx].gatts_cb) {
                    gl_profile_tab[idx].gatts_cb(event, gatts_if, param);
                }
            }
        }
    } while (0);
}



static void gap_event_handler(esp_gap_ble_cb_event_t event, esp_ble_gap_cb_param_t *param)
{
    // ESP_LOGI(TAG, "GAP_EVT, event %d\n", event);
    switch (event) {
    case ESP_GAP_BLE_SCAN_RSP_DATA_SET_COMPLETE_EVT:ESP_LOGI("BT","ESP_GAP_BLE_SCAN_RSP_DATA_SET_COMPLETE_EVT");
        adv_config_done &= (~scan_rsp_config_flag);
        if (adv_config_done == 0){
            init_done = true;
            if (allow_conn_when_init_done) {
                allow_connections();
                allow_conn_when_init_done = false;
            }    
        }
        break;
    case ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT:ESP_LOGI("BT","ESP_GAP_BLE_ADV_DATA_SET_COMPLETE_EVT");
        adv_config_done &= (~adv_config_flag);
        if (adv_config_done == 0){
            init_done = true;
            if (allow_conn_when_init_done) {
                allow_connections();
            } 
        }
        break;
    case ESP_GAP_BLE_ADV_START_COMPLETE_EVT:ESP_LOGI("BT","ESP_GAP_BLE_ADV_START_COMPLETE_EVT");
        //advertising start complete event to indicate advertising start successfully or failed
        if (param->adv_start_cmpl.status != ESP_BT_STATUS_SUCCESS) {
            ESP_LOGW(TAG, "advertising start failed, err: %x", param->adv_start_cmpl.status);
            break;
        }
        ESP_LOGI(TAG, "advertising started");
        break;
    case ESP_GAP_BLE_PASSKEY_REQ_EVT:    ESP_LOGI("BT","ESP_GAP_BLE_PASSKEY_REQ_EVT");                       /* passkey request event */
        ESP_LOGI(TAG, "ESP_GAP_BLE_PASSKEY_REQ_EVT");
        /* Call the following function to input the passkey which is displayed on the remote device */
        //esp_ble_passkey_reply(heart_rate_profile_tab[HEART_PROFILE_APP_IDX].remote_bda, true, 0x00);
        break;
    case ESP_GAP_BLE_OOB_REQ_EVT: {ESP_LOGI("BT","ESP_GAP_BLE_OOB_REQ_EVT");
        ESP_LOGI(TAG, "ESP_GAP_BLE_OOB_REQ_EVT");
        // uint8_t tk[16] = {1}; //If you paired with OOB, both devices need to use the same tk
        // esp_ble_oob_req_reply(param->ble_security.ble_req.bd_addr, tk, sizeof(tk));
        break;
    }
    case ESP_GAP_BLE_LOCAL_IR_EVT:   
        ESP_LOGW(TAG, "ESP_GAP_BLE_LOCAL_IR_EVT");
        break;
    case ESP_GAP_BLE_LOCAL_ER_EVT:   
        ESP_LOGW(TAG, "ESP_GAP_BLE_LOCAL_ER_EVT");
        break;
    case ESP_GAP_BLE_NC_REQ_EVT:
        /* The app will receive this evt when the IO has DisplayYesNO capability and the peer device IO also has DisplayYesNo capability.
        show the passkey number to the user to confirm it with the number displayed by peer device. */
        //esp_ble_confirm_reply(param->ble_security.ble_req.bd_addr, true);
        ESP_LOGI(TAG, "ESP_GAP_BLE_NC_REQ_EVT, the passkey Notify number:%lu", param->ble_security.key_notif.passkey);
        break;
    case ESP_GAP_BLE_SEC_REQ_EVT:
        ESP_LOGI(TAG,"ESP_GAP_BLE_SEC_REQ_EVT");
        /* send the positive(true) security response to the peer device to accept the security request.
        If not accept the security request, should send the security response with negative(false) accept value*/
        esp_ble_gap_security_rsp(param->ble_security.ble_req.bd_addr, false);
        break;
    case ESP_GAP_BLE_PASSKEY_NOTIF_EVT: 
        ///show the passkey number to the user to input it in the peer device.
        ESP_LOGI(TAG, "The passkey Notify number:%06ld", param->ble_security.key_notif.passkey);
        break;
    case ESP_GAP_BLE_KEY_EVT:
        //shows the ble key info share with peer device to the user.
        ESP_LOGI(TAG, "key event");
        break;
    case ESP_GAP_BLE_AUTH_CMPL_EVT:
        ESP_LOGE(TAG,"ESP_GAP_BLE_AUTH_CMPL_EVT");
        esp_bd_addr_t bd_addr;
        memcpy(bd_addr, param->ble_security.auth_cmpl.bd_addr, sizeof(esp_bd_addr_t));
        ESP_LOGI(TAG, "remote BD_ADDR: %08x%04x",\
                (bd_addr[0] << 24) + (bd_addr[1] << 16) + (bd_addr[2] << 8) + bd_addr[3],
                (bd_addr[4] << 8) + bd_addr[5]);
        ESP_LOGI(TAG, "address type = %d", param->ble_security.auth_cmpl.addr_type);
        ESP_LOGI(TAG, "pair status = %s",param->ble_security.auth_cmpl.success ? "success" : "fail");
        if(!param->ble_security.auth_cmpl.success)
        {
            ESP_LOGW(TAG, "fail reason = 0x%x",param->ble_security.auth_cmpl.fail_reason);
        } else
        {
            ESP_LOGI(TAG, "auth mode = %s", esp_auth_req_to_str(param->ble_security.auth_cmpl.auth_mode));
            connected = true;
            stop_connect_timer();
        }
        break;
    case ESP_GAP_BLE_REMOVE_BOND_DEV_COMPLETE_EVT:
        ESP_LOGI(TAG, "ESP_GAP_BLE_REMOVE_BOND_DEV_COMPLETE_EVT status = %d", param->remove_bond_dev_cmpl.status);
        esp_log_buffer_hex(TAG, (void *)param->remove_bond_dev_cmpl.bd_addr, sizeof(esp_bd_addr_t));
        break;
    case ESP_GAP_BLE_SET_LOCAL_PRIVACY_COMPLETE_EVT:ESP_LOGE("BT","ESP_GAP_BLE_SET_LOCAL_PRIVACY_COMPLETE_EVT");
        {
        if (param->local_privacy_cmpl.status != ESP_BT_STATUS_SUCCESS){
            ESP_LOGW(TAG, "config local privacy failed, error status = %x", param->local_privacy_cmpl.status);
            break;
        }
        esp_err_t ret = esp_ble_gap_config_adv_data(&adv_data);
        if (ret){
            ESP_LOGW(TAG, "config adv data failed, error code = %x", ret);
        }else{
            adv_config_done |= adv_config_flag;
        }
        ret = esp_ble_gap_config_adv_data(&scan_rsp_data);
        if (ret){
            ESP_LOGW(TAG, "config adv data failed, error code = %x", ret);
        }else{
            adv_config_done |= scan_rsp_config_flag;
        }
        break;
        }
    default:
        break;
    }
}




/*========================================================================*\
 * CONTROL GATTS CALLBACK
\*========================================================================*/
static void gatts_profile_control_event_handler(esp_gatts_cb_event_t event, esp_gatt_if_t gatts_if, esp_ble_gatts_cb_param_t *param)
{
    switch (event) {
    case ESP_GATTS_REG_EVT:
    {
        ESP_LOGI(TAG, "REGISTER_APP_EVT, status %d, app_id %d\n", param->reg.status, param->reg.app_id);
        esp_err_t set_dev_name_ret = esp_ble_gap_set_device_name((const char*)device_name);
        if (set_dev_name_ret)
        {
            ESP_LOGE(TAG, "set device name failed, error code = %x", set_dev_name_ret);
        }
        // esp_ble_gap_config_local_privacy(true);

        //config adv data
        esp_err_t ret = esp_ble_gap_config_adv_data(&adv_data);
        if (ret)
        {
            ESP_LOGE(TAG, "config adv data failed, error code = %x", ret);
        }
        adv_config_done |= adv_config_flag;
        //config scan response data
        ret = esp_ble_gap_config_adv_data(&scan_rsp_data);
        if (ret)
        {
            ESP_LOGE(TAG, "config scan response data failed, error code = %x", ret);
        }
        adv_config_done |= scan_rsp_config_flag;

        esp_err_t create_attr_ret = esp_ble_gatts_create_attr_tab(gatt_db, gatts_if, ATTRIBUTES_QTY, PROFILE_CONTROL);
        if (create_attr_ret){
            ESP_LOGE(TAG, "create attr table failed, error code = %x", create_attr_ret);
        } else {
            ESP_LOGW(TAG, "\n attributes table created \n");
        }
        break;
    }
    case ESP_GATTS_READ_EVT: 
        {
        ESP_LOGI(TAG, "GATT_READ_EVT, conn_id %d, trans_id %ld, handle %d\n", param->read.conn_id, param->read.trans_id, param->read.handle);
        if(param->read.handle == handle_table[CHAR_TERM_OUT_VALUE])
        {
            logs_sent = true;
        }
        break;
        }
    case ESP_GATTS_WRITE_EVT: 
        {
        ESP_LOGI(TAG,"\n");
        ESP_LOGI(TAG, "GATT_WRITE_EVT, value len %d", param->write.len);
        ESP_LOG_BUFFER_HEXDUMP (TAG, param->write.value, param->write.len, ESP_LOG_INFO);

        // descriptors setts
        if (!param->write.is_prep){
            // ESP_LOGI(TAG, "    value len %d", param->write.len);
            // esp_log_buffer_hex(TAG, param->write.value, param->write.len);
            if ((handle_table[CHAR_TX_CFG] == param->write.handle 
                    || handle_table[CHAR_STATUS_CFG] == param->write.handle
                    || handle_table[CHAR_TERM_OUT_CFG] == param->write.handle
                )
                 && param->write.len == 2)
            {
                uint16_t descr_value= param->write.value[1]<<8 | param->write.value[0];
                if (descr_value == 0x0001)
                {
                    ESP_LOGI(TAG, "notify enable");
                    uint8_t notify_data[15];
                    for (int i = 0; i < sizeof(notify_data); ++i)
                    {
                        notify_data[i] = i%0xff;
                    }
                    esp_ble_gatts_send_indicate(gatts_if, param->write.conn_id, param->write.handle,
                                                sizeof(notify_data), notify_data, false);

                    if(handle_table[CHAR_TERM_OUT_CFG] == param->write.handle) terminal_subscription = true;
                } else if (descr_value == 0x0002){
                    ESP_LOGI(TAG, "indicate enable");
                    uint8_t indicate_data[15];
                    for (int i = 0; i < sizeof(indicate_data); ++i)
                    {
                        indicate_data[i] = i%0xff;
                    }
                    esp_ble_gatts_send_indicate(gatts_if, param->write.conn_id, param->write.handle,
                                                sizeof(indicate_data), indicate_data, true);

                    if(handle_table[CHAR_TERM_OUT_CFG] == param->write.handle) terminal_subscription = true;
                } else if (descr_value == 0x0000) {
                    ESP_LOGI(TAG, "notify/indicate disable ");
                    if(handle_table[CHAR_TERM_OUT_CFG] == param->write.handle) terminal_subscription = false;
                } else {
                    ESP_LOGE(TAG, "err: unknown value of descriptor");
                }
        }

        if(param->write.handle == handle_table[CHAR_RX_VALUE])
        {
            if (param->write.len > GATTS_RX_VAL_LEN_MAX)
            {
                ESP_LOGE (TAG,"too big packet from phone");
            } else {
                memcpy(rx_value_arr, param->write.value, param->write.len);
                write_event_env(gatts_if, &a_prepare_write_env, param);

                got_ble_packet(rx_value_arr, param->write.len);
            }
        } else if(param->write.handle == handle_table[CHAR_TERM_IN_VALUE]){
            if (param->write.len >= GATTS_TERMINAL_IN_VAL_LEN_MAX)
            {
                ESP_LOGE (TAG,"too big command from phone");
            } else {
                memset(terminal_in_arr, 0, GATTS_TERMINAL_IN_VAL_LEN_MAX);
                memcpy(terminal_in_arr, param->write.value, param->write.len);
                write_event_env(gatts_if, &a_prepare_write_env, param);

                settings->ble_command_executor(terminal_in_arr, param->write.len);
            }
        } else {
        	// esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, ESP_GATT_WRITE_NOT_PERMIT, NULL);
        }
        break;
    }
    case ESP_GATTS_MTU_EVT:
        ESP_LOGI(TAG, "ESP_GATTS_MTU_EVT, MTU %d", param->mtu.mtu);
        break;
    case ESP_GATTS_UNREG_EVT:
    	ESP_LOGI(TAG, "ESP_GATTS_UNREG_EVT");
        break;
    case ESP_GATTS_CREAT_ATTR_TAB_EVT:{
        if (param->add_attr_tab.status != ESP_GATT_OK){
            ESP_LOGE(TAG, "create attribute table failed, error code=0x%x", param->add_attr_tab.status);
        }
        else if (param->add_attr_tab.num_handle != ATTRIBUTES_QTY){
            ESP_LOGE(TAG, "create attribute table abnormally, num_handle (%d) \
                    doesn't equal to HRS_IDX_NB(%d)", param->add_attr_tab.num_handle, ATTRIBUTES_QTY);
        }
        else {
            ESP_LOGI(TAG, "create attribute table successfully, the number handle = %d",param->add_attr_tab.num_handle);
            memcpy(handle_table, param->add_attr_tab.handles, sizeof(handle_table));
            esp_ble_gatts_start_service(handle_table[BORK_CONTROL_SERVICE]);
        }
        break;
    }

    case ESP_GATTS_ADD_INCL_SRVC_EVT:
    	ESP_LOGI(TAG, "ESP_GATTS_ADD_INCL_SRVC_EVT");
        break;

    case ESP_GATTS_ADD_CHAR_EVT: {
        uint16_t length = 0;
        const uint8_t *prf_char;
        ESP_LOGI(TAG, "ADD_CHAR_EVT, status %d,  attr_handle %d, char uuid %04x",
                param->add_char.status, param->add_char.attr_handle, param->add_char.char_uuid.uuid.uuid16);
        }
        break;
    }

    case ESP_GATTS_ADD_CHAR_DESCR_EVT:
        ESP_LOGI(TAG, "ADD_DESCR_EVT, status %d, attr_handle %d, descriptor uuid: %04x",
                 param->add_char_descr.status, param->add_char_descr.attr_handle, param->add_char_descr.descr_uuid.uuid.uuid16);
        break;
    case ESP_GATTS_DELETE_EVT:
    	ESP_LOGI(TAG, "ESP_GATTS_DELETE_EVT");
        break;
    case ESP_GATTS_START_EVT:
        ESP_LOGI(TAG, "SERVICE_START_EVT, status %d, service_handle %d\n",
                 param->start.status, param->start.service_handle);
        break;
    case ESP_GATTS_STOP_EVT:
    	ESP_LOGI(TAG, "ESP_GATTS_STOP_EVT");
        break;
    case ESP_GATTS_CONNECT_EVT: {
        esp_event_post_to(settings->loop_handle, BLE_EVENTS, BLE_CONNECTED, NULL, 0, 0);
        stop_connect_timer();

    	ESP_LOGI(TAG,"ESP_GATTS_CONNECT_EVT");
        if(! accept_connection)
        {
        	ESP_LOGI(TAG, "connections not allowed");
        	esp_ble_gatts_close(gatts_if, param->connect.conn_id);
        	break;
        }
        gl_profile_tab[PROFILE_CONTROL].conn_id = param->connect.conn_id;
        connected = true;

        ESP_LOGW (TAG, "\n=============\nBLE connected\n=============");

        break;
    }
    case ESP_GATTS_DISCONNECT_EVT:
        esp_event_post_to(settings->loop_handle, BLE_EVENTS, BLE_DISCONNECTED, NULL, 0, 0);

        ESP_LOGW(TAG, "ESP_GATTS_DISCONNECT_EVT, disconnect reason 0x%x", param->disconnect.reason);
        ESP_LOGI(TAG, "ESP_GATTS_DISCONNECT_EVT, conn_id %d, remote %02x:%02x:%02x:%02x:%02x:%02x:",
                         param->disconnect.conn_id,
                         param->disconnect.remote_bda[0], param->disconnect.remote_bda[1], param->disconnect.remote_bda[2],
                         param->disconnect.remote_bda[3], param->disconnect.remote_bda[4], param->disconnect.remote_bda[5]);

        connected = false;

        ESP_LOGW (TAG, "\n================\nBLE disconnected\n================");

        allow_connections();
        start_connect_timer(CONNECTION_TIME);

        break;

    case ESP_GATTS_CONF_EVT:
        #if BLE_LOGS
        ESP_LOGI(TAG, "ESP_GATTS_CONF_EVT, status %d attr_handle %d", param->conf.status, param->conf.handle);
        #endif
        if (param->conf.status != ESP_GATT_OK){
            esp_log_buffer_hex(TAG, param->conf.value, param->conf.len);
        }
        break;

    case ESP_GATTS_OPEN_EVT:
    case ESP_GATTS_CANCEL_OPEN_EVT:
    case ESP_GATTS_CLOSE_EVT:
    case ESP_GATTS_LISTEN_EVT:
    case ESP_GATTS_CONGEST_EVT:
    default:
        break;
    }
}



static void write_event_env(esp_gatt_if_t gatts_if, prepare_type_env_t *prepare_write_env, esp_ble_gatts_cb_param_t *param){
    esp_gatt_status_t status = ESP_GATT_OK;
    if (param->write.need_rsp)
    {
        if (param->write.is_prep)
        {
            if (prepare_write_env->prepare_buf == NULL)
            {
                prepare_write_env->prepare_buf = (uint8_t *)malloc(PREPARE_BUF_MAX_SIZE*sizeof(uint8_t));
                prepare_write_env->prepare_len = 0;
                if (prepare_write_env->prepare_buf == NULL)
                {
                    ESP_LOGE(TAG, "Gatt_server prep no mem\n");
                    status = ESP_GATT_NO_RESOURCES;
                }
            }
            else
            {
                if(param->write.offset > PREPARE_BUF_MAX_SIZE)
                {
                    status = ESP_GATT_INVALID_OFFSET;
                }
                else if ((param->write.offset + param->write.len) > PREPARE_BUF_MAX_SIZE)
                {
                    status = ESP_GATT_INVALID_ATTR_LEN;
                }
            }
            esp_gatt_rsp_t *gatt_rsp = (esp_gatt_rsp_t *)malloc(sizeof(esp_gatt_rsp_t));
            gatt_rsp->attr_value.len = param->write.len;
            gatt_rsp->attr_value.handle = param->write.handle;
            gatt_rsp->attr_value.offset = param->write.offset;
            gatt_rsp->attr_value.auth_req = ESP_GATT_AUTH_REQ_NONE;
            memcpy(gatt_rsp->attr_value.value, param->write.value, param->write.len);
            // esp_err_t response_err = esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, status, gatt_rsp);
            // if (response_err != ESP_OK)
            // {
            //    ESP_LOGW(TAG, "Send response error\n");
            // }
            free(gatt_rsp);
            if (status != ESP_GATT_OK)
            {
                return;
            }
            memcpy(prepare_write_env->prepare_buf + param->write.offset,
                   param->write.value,
                   param->write.len);
            prepare_write_env->prepare_len += param->write.len;
        }
        else
        {
            // esp_ble_gatts_send_response(gatts_if, param->write.conn_id, param->write.trans_id, status, NULL);
        }
    }
}



static void got_ble_packet(uint8_t *data, uint16_t length)
{
    if (!settings->incoming_packet_check((const char *)data, length))
    {
        ESP_LOGW(TAG,"ble incoming packet is wrong");
        return;
    }

    ble_queue_obj_t inc_data;
    inc_data.data_len = length;
    memcpy(inc_data.data, data, length);

    if (xQueueSend(*settings->receiving_queue, &inc_data, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"ble incoming queue is full");
    }
}



static const char *esp_auth_req_to_str(esp_ble_auth_req_t auth_req)
{
   const char *auth_str = NULL;
   switch(auth_req) {
    case ESP_LE_AUTH_NO_BOND:
        auth_str = "ESP_LE_AUTH_NO_BOND";
        break;
    case ESP_LE_AUTH_BOND:
        auth_str = "ESP_LE_AUTH_BOND";
        break;
    case ESP_LE_AUTH_REQ_MITM:
        auth_str = "ESP_LE_AUTH_REQ_MITM";
        break;
    // case ESP_LE_AUTH_REQ_BOND_MITM:
        // auth_str = "ESP_LE_AUTH_REQ_BOND_MITM";
        // break;
    case ESP_LE_AUTH_REQ_SC_ONLY:
        auth_str = "ESP_LE_AUTH_REQ_SC_ONLY";
        break;
    case ESP_LE_AUTH_REQ_SC_BOND:
        auth_str = "ESP_LE_AUTH_REQ_SC_BOND";
        break;
    case ESP_LE_AUTH_REQ_SC_MITM:
        auth_str = "ESP_LE_AUTH_REQ_SC_MITM";
        break;
    case ESP_LE_AUTH_REQ_SC_MITM_BOND:
        auth_str = "ESP_LE_AUTH_REQ_SC_MITM_BOND";
        break;
    default:
        auth_str = "INVALID BLE AUTH REQ";
        break;
   }

   return auth_str;
}



} // namespace bsh_sys::ble