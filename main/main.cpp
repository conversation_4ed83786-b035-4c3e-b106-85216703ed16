#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_ota_ops.h"
#include <inttypes.h>

#include "main_task.h"
#include "device_specific_data.h"
#include "rust_functions.h"

static const char *TAG = "MAIN_APP";
static TaskHandle_t HandleMainTask;

extern "C" void app_main() {

    ESP_LOGI(TAG, "[APP] Startup..");
    ESP_LOGI(TAG, "[APP] Free memory: %lu bytes", esp_get_free_heap_size());
    ESP_LOGI(TAG, "[APP] IDF version: %s", esp_get_idf_version());
    printf("project name: %s\n", esp_ota_get_app_description()->project_name);
    printf("commit hash: %s\n", esp_ota_get_app_description()->version);
    printf("build date: %s_%s\n", __DATE__, __TIME__);
    printf("firmware version: %i.%i.%i\n", FIRMWARE_VERSION_MAJOR, FIRMWARE_VERSION_MINOR, FIRMWARE_VERSION_PATCH);


    // Initialize NVS
    esp_err_t ret;
    ret = nvs_flash_init();
    ESP_LOGI(TAG, "NVS init done");
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK( ret );

    ESP_LOGI(TAG, "Success boot heap: %u", xPortGetFreeHeapSize());

    // Тест новых Rust функций
    ESP_LOGI(TAG, "=== Тестирование Rust библиотеки ===");

    // Тест actor_code_to_string
    const char* mod_name = a630::mqtt_protocol::actor_code_to_string(static_cast<a630::mqtt_protocol::actor_t>(0)); // MOD
    ESP_LOGI(TAG, "Actor 0 name: %s", mod_name ? mod_name : "NULL");

    // Тест get_actor_id
    const char test_code[] = {'M', 'O', 'D'}; // не null-terminated
    a630::mqtt_protocol::actor_t found_id = a630::mqtt_protocol::get_actor_id(test_code);
    ESP_LOGI(TAG, "Actor ID for 'MOD': %d", static_cast<int>(found_id));

    // Create tasks
    ret = xTaskCreate(a630::main_task::main_task, "main_task", 4096*2, NULL, 12, &HandleMainTask);
    if (ret != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create main task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }
    ESP_LOGI(TAG, "Main task created");

}
