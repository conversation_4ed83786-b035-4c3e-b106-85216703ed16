#![cfg(feature = "test")]
#[allow(unused_unsafe)]



use time::Month;

use a630_rust_lib::date_and_time::{parse_and_store_http_date, get_date, current_datetime_string,};
use a630_rust_lib::_esp_idf_sys_mocks::set_esp_timer_mock;



#[test]
fn parse_and_store_http_date_test() {
    let date_str = "Fri, 05 May 2023 07:14:26 GMT";
    parse_and_store_http_date(date_str).unwrap();
    assert_eq!(get_date().unwrap().year(), 2023);
    assert_eq!(get_date().unwrap().month(), Month::May);
    assert_eq!(get_date().unwrap().day(), 5);
    assert_eq!(get_date().unwrap().hour(), 7);
    assert_eq!(get_date().unwrap().minute(), 14);
    assert_eq!(get_date().unwrap().second(), 26);
}

#[test]
fn current_datetime_string_test() {
    let date_str = "Fri, 05 May 2023 07:14:26 GMT";
    parse_and_store_http_date(date_str).unwrap();
    let datetime_str = current_datetime_string();
    assert_eq!(datetime_str, "2023-05-05T07:14:26");
}

#[test]
fn time_counts_properly_test() {
    let date_str = "Fri, 05 May 2023 07:14:26 GMT";
    parse_and_store_http_date(date_str).unwrap();

    set_esp_timer_mock(1_000_000); // 1 second
    assert_eq!(get_date().unwrap().second(), 27);

    set_esp_timer_mock(60_000_000); // 1 minute
    assert_eq!(get_date().unwrap().minute(), 15);

    set_esp_timer_mock(3_600_000_000); // 1 hour
    assert_eq!(get_date().unwrap().hour(), 8);

    set_esp_timer_mock(86_400_000_000); // 1 day
    assert_eq!(get_date().unwrap().day(), 6);
}
