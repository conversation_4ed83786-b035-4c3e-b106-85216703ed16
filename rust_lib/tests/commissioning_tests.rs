#![cfg(feature = "test")] // для тестов

use a630_rust_lib::commissioning::{init, DEVICE_ID_SIZE, GUID_SIZE, get_device_id, get_device_id_as_string}; 
use a630_rust_lib::commissioning::{generate_guid, get_guid};
use a630_rust_lib::commissioning::{write_device_id_to_buffer_as_string, write_device_id_to_buffer};
use a630_rust_lib::commissioning::{set_wifi_ssid, get_wifi_ssid, MAX_PARAMETER_LEN};
use a630_rust_lib::commissioning::{set_wifi_password, get_wifi_password};
use a630_rust_lib::commissioning::{set_mqtt_user_id, get_mqtt_user_id};
use a630_rust_lib::commissioning::{set_mqtt_password, get_mqtt_password};
use a630_rust_lib::commissioning::{set_balancer_link, get_balancer_link};



/*
        SSID
*/
#[test]
fn wifi_ssid_tests() {
    test_get_ssid_none();
    test_set_and_get_wifi_ssid();
    test_unicode_ssid();
    test_too_long_ssid();
}

fn test_set_and_get_wifi_ssid() {

    // Установка SSID
    let ssid = "IntegrationWiFi";
    let result = set_wifi_ssid(ssid);
    assert!(result.is_ok(), "set_wifi_ssid вернула ошибку");

    // Проверка, что значение сохранилось
    let stored_ssid = get_wifi_ssid();
    assert_eq!(stored_ssid, Some(ssid.to_string()), "SSID не сохранился");

    // Перезапись SSID
    let new_ssid = "NewIntegrationWiFi";
    set_wifi_ssid(new_ssid).unwrap();
    assert_eq!(get_wifi_ssid(), Some(new_ssid.to_string()), "SSID не перезаписался");
}

fn test_get_ssid_none() {
    assert!(get_wifi_ssid().is_none());
}

fn test_unicode_ssid() {
    let ssid = "пароль_😊";
    let result = set_wifi_ssid(ssid);
    assert!(result.is_ok());
    assert_eq!(get_wifi_ssid().unwrap(), ssid);
}

fn test_too_long_ssid() {
    let too_long = "a".repeat(MAX_PARAMETER_LEN + 1);
    let result = set_wifi_ssid(&too_long);
    assert!(result.is_err());
}



#[test]
fn wifi_password_tests() {
    test_get_password_none();
    test_set_and_get_wifi_pswd();
    test_unicode_password();
    test_too_long_password();
}

fn test_get_password_none() {
    assert!(get_wifi_password().is_none());
}

fn test_unicode_password() {
    let password = "пароль_😊";
    let result = set_wifi_password(password);
    assert!(result.is_ok());
    assert_eq!(get_wifi_password().unwrap(), password);
}

fn test_set_and_get_wifi_pswd() {

    // Установка пароля
    let pswd = "IntegrationWiFiPassword";
    let result = set_wifi_password(pswd);
    assert!(result.is_ok(), "set_wifi_password вернула ошибку");

    // Проверка, что значение сохранилось
    let stored_pswd = get_wifi_password();
    assert_eq!(stored_pswd, Some(pswd.to_string()), "Пароль не сохранился");    

    // Перезапись пароля
    let new_pswd = "NewIntegrationWiFiPassword";
    set_wifi_password(new_pswd).unwrap();
    assert_eq!(get_wifi_password(), Some(new_pswd.to_string()), "Пароль не перезаписался");
}

fn test_too_long_password() {
    let too_long = "a".repeat(MAX_PARAMETER_LEN + 1);
    let result = set_wifi_password(&too_long);
    assert!(result.is_err());
}



#[test]
fn mqtt_user_id_tests() {
    test_get_mqtt_id_none();
    test_set_and_get_mqtt_id();
    test_unicode_mqtt_id();
    test_too_long_mqtt_id();
}

fn test_get_mqtt_id_none() {
    assert!(get_mqtt_user_id().is_none());
}

fn test_unicode_mqtt_id() {
    let mqtt_id = "пароль_😊";
    let result = set_mqtt_user_id(mqtt_id);
    assert!(result.is_ok());
    assert_eq!(get_mqtt_user_id().unwrap(), mqtt_id);
}   

fn test_too_long_mqtt_id() {
    let too_long = "a".repeat(MAX_PARAMETER_LEN + 1);
    let result = set_mqtt_user_id(&too_long);
    assert!(result.is_err());
}

fn test_set_and_get_mqtt_id() {
    // Установка MQTT ID
    let mqtt_id = "IntegrationMQTTID";
    let result = set_mqtt_user_id(mqtt_id);
    assert!(result.is_ok(), "set_mqtt_user_id вернула ошибку");

    // Проверка, что значение сохранилось
    let stored_mqtt_id = get_mqtt_user_id();
    assert_eq!(stored_mqtt_id, Some(mqtt_id.to_string()), "MQTT ID не сохранился");

    // Перезапись MQTT ID
    let new_mqtt_id = "NewIntegrationMQTTID";
    set_mqtt_user_id(new_mqtt_id).unwrap();
    assert_eq!(get_mqtt_user_id(), Some(new_mqtt_id.to_string()), "MQTT ID не перезаписался");
}



#[test]
fn mqtt_password_tests() {
    test_get_mqtt_passw_none();
    test_set_and_get_mqtt_passw();
    test_unicode_mqtt_passw();
    test_too_long_mqtt_passw();
}

fn test_get_mqtt_passw_none() {
    assert!(get_mqtt_password().is_none());
}

fn test_unicode_mqtt_passw() {
    let mqtt_passw = "пароль_😊";
    let result = set_mqtt_password(mqtt_passw);
    assert!(result.is_ok());
    assert_eq!(get_mqtt_password().unwrap(), mqtt_passw);
}

fn test_too_long_mqtt_passw() {
    let too_long = "a".repeat(MAX_PARAMETER_LEN + 1);
    let result = set_mqtt_password(&too_long);
    assert!(result.is_err());
}

fn test_set_and_get_mqtt_passw() {
    let mqtt_passw = "IntegrationMQTTPassword";
    let result = set_mqtt_password(mqtt_passw);
    assert!(result.is_ok(), "set_mqtt_password вернула ошибку");

    let stored_mqtt_passw = get_mqtt_password();
    assert_eq!(stored_mqtt_passw, Some(mqtt_passw.to_string()), "MQTT пароль не сохранился");

    let new_mqtt_passw = "NewIntegrationMQTTPassword";
    set_mqtt_password(new_mqtt_passw).unwrap();
    assert_eq!(get_mqtt_password(), Some(new_mqtt_passw.to_string()), "MQTT пароль не перезаписался");
}



#[test]
fn balancer_link_tests() {
    test_get_balancer_link_none();
    test_set_and_get_balancer_link();
    test_unicode_balancer_link();
    test_too_long_balancer_link();
}

fn test_get_balancer_link_none() {
    assert!(get_balancer_link().is_none());
}

fn test_unicode_balancer_link() {
    let balancer_link = "http://пароль_😊.balancer.link";
    let result = set_balancer_link(balancer_link);
    assert!(result.is_ok());
    assert_eq!(get_balancer_link().unwrap(), balancer_link);
}

fn test_too_long_balancer_link() {
    let too_long = "a".repeat(MAX_PARAMETER_LEN + 1);
    let result = set_balancer_link(&too_long);
    assert!(result.is_err());
}

fn test_set_and_get_balancer_link() {
    let balancer_link = "http://integration.balancer.link";
    let result = set_balancer_link(balancer_link);
    assert!(result.is_ok(), "set_balancer_link вернула ошибку");

    let stored_balancer_link = get_balancer_link();
    assert_eq!(stored_balancer_link, Some(balancer_link.to_string()), "balancer link не сохранился");

    let new_balancer_link = "http://new.integration.balancer.link";
    set_balancer_link(new_balancer_link).unwrap();
    assert_eq!(get_balancer_link(), Some(new_balancer_link.to_string()), "balancer link не перезаписался");
}



#[test]
fn device_id_tests() {
    test_init();
    test_get_device_id();
    test_get_device_id_as_string();
    test_write_device_id_to_buffer();
    test_write_device_id_to_buffer_as_string();
    test_generate_guid();
    test_get_guid();
}

fn test_init() {
    init([1u8, 2, 3, 4, 5, 6]).unwrap();
}

fn test_get_device_id() {
    let device_id = get_device_id();
    assert_eq!(device_id, [42u8, 59, 76, 93, 1, 2, 3, 4, 5, 6]);
}

fn test_get_device_id_as_string() {
    let device_id = get_device_id_as_string();
    assert_eq!(device_id, "2a3b4c5d010203040506");
}

fn test_write_device_id_to_buffer() {
    let mut buffer = [0u8; DEVICE_ID_SIZE];
    write_device_id_to_buffer(&mut buffer);
    assert_eq!(buffer, [42u8, 59, 76, 93, 1, 2, 3, 4, 5, 6]);
}

fn test_write_device_id_to_buffer_as_string() {
    let mut buffer = [0u8; DEVICE_ID_SIZE * 2 + 1];
    let len = write_device_id_to_buffer_as_string(&mut buffer);
    assert_eq!(len, DEVICE_ID_SIZE * 2);
    assert_eq!(&buffer[..len], b"2a3b4c5d010203040506");
}

fn test_generate_guid() {
    let mut buffer = [0u8; GUID_SIZE];
    generate_guid("bork_device_", &mut buffer).unwrap();
    assert_eq!(&buffer[..12], b"bork_device_");
}

fn test_get_guid() {
    let guid = get_guid("bork_device_").unwrap();
    assert_eq!(&guid[..12], "bork_device_");
}
