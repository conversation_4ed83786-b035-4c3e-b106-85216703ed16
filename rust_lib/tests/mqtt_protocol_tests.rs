#![cfg(feature = "test")]

use a630_rust_lib::mqtt_protocol;


#[test]
fn test_get_by_id(){
    assert_eq!(mqtt_protocol::get_by_id(0).unwrap().mnemocode, b"MOD");
    assert_eq!(mqtt_protocol::get_by_id(1).unwrap().mnemocode, b"STA");
    assert_eq!(mqtt_protocol::get_by_id(2).unwrap().mnemocode, b"ARA");
    assert_eq!(mqtt_protocol::get_by_id(3).unwrap().mnemocode, b"WLP");
    assert_eq!(mqtt_protocol::get_by_id(4).unwrap().mnemocode, b"ALP");
    assert_eq!(mqtt_protocol::get_by_id(5).unwrap().mnemocode, b"STS");
    assert_eq!(mqtt_protocol::get_by_id(6).unwrap().mnemocode, b"STE");
    assert_eq!(mqtt_protocol::get_by_id(7).unwrap().mnemocode, b"SCN");
    assert_eq!(mqtt_protocol::get_by_id(8).unwrap().mnemocode, b"FSC");

    assert_eq!(mqtt_protocol::get_by_id(1000), None);
}



#[test]
fn test_get_by_code(){
    assert_eq!(mqtt_protocol::get_by_code(b"MOD").unwrap().id, 0);
    assert_eq!(mqtt_protocol::get_by_code(b"STA").unwrap().id, 1);
    assert_eq!(mqtt_protocol::get_by_code(b"ARA").unwrap().id, 2);
    assert_eq!(mqtt_protocol::get_by_code(b"WLP").unwrap().id, 3);
    assert_eq!(mqtt_protocol::get_by_code(b"ALP").unwrap().id, 4);
    assert_eq!(mqtt_protocol::get_by_code(b"STS").unwrap().id, 5);

    assert_eq!(mqtt_protocol::get_by_code(b"XXX"), None);
}