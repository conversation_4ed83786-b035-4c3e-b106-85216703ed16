cmake_minimum_required(VERSION 3.5)

# Переменные для Rust
get_filename_component(PROJECT_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR} DIRECTORY)
set(RUST_PROJECT_DIR ${PROJECT_ROOT_DIR}/rust_lib)
set(RUST_TARGET_DIR ${RUST_PROJECT_DIR}/target)
set(RUST_LIB_NAME "liba630_rust_lib.a")

message(STATUS "Project root: ${PROJECT_ROOT_DIR}")
message(STATUS "Rust project dir: ${RUST_PROJECT_DIR}")

# Целевая архитектура для ESP32 (автоматически определяем из IDF_TARGET)
if(DEFINED ENV{IDF_TARGET})
    if($ENV{IDF_TARGET} STREQUAL "esp32s3")
        set(RUST_TARGET "xtensa-esp32s3-espidf")
    elseif($ENV{IDF_TARGET} STREQUAL "esp32")
        set(RUST_TARGET "xtensa-esp32-espidf")
    else()
        set(RUST_TARGET "xtensa-esp32s3-espidf")  # По умолчанию ESP32-S3
    endif()
else()
    set(RUST_TARGET "xtensa-esp32s3-espidf")  # По умолчанию ESP32-S3
endif()

message(STATUS "Rust target: ${RUST_TARGET}")

# Путь к статической библиотеке
set(RUST_LIB_PATH "${RUST_TARGET_DIR}/${RUST_TARGET}/release/${RUST_LIB_NAME}")

message(STATUS "Ожидаемый путь к Rust библиотеке: ${RUST_LIB_PATH}")

# Проверка, что Rust библиотека в наличии
if(NOT EXISTS ${RUST_LIB_PATH})
    message(FATAL_ERROR
        "Rust библиотека не найдена!\n"
        "Ожидаемый путь:\n"
        "  ${RUST_LIB_PATH}\n"
        "\n"
        "Сначала соберите Rust библиотеку скриптом:\n"
        "  ./.scripts/build_rust_in_docker.sh\n"
        "\n"
        "Текущая директория CMake: ${CMAKE_CURRENT_SOURCE_DIR}\n"
        "Корневая директория проекта: ${CMAKE_SOURCE_DIR}\n"
    )
endif()

message(STATUS "Найдена Rust библиотека: ${RUST_LIB_PATH}")

# Регистрирация компонента
idf_component_register(
    INCLUDE_DIRS "include"
    REQUIRES ""
)

# Добавление статической библиотеки
add_library(rust_static_lib STATIC IMPORTED GLOBAL)
set_target_properties(rust_static_lib PROPERTIES
    IMPORTED_LOCATION ${RUST_LIB_PATH}
)

# Линковка библиотеки к компоненту
target_link_libraries(${COMPONENT_LIB} INTERFACE rust_static_lib)

message(STATUS "Rust библиотека подключена к компоненту")
