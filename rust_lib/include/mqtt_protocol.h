#pragma once
#include <string_view>

#include "date_and_time.h"



namespace a630::mqtt_protocol {



// extern "C" {
    
    

/** 
* Actors described at https://youtrack.bork.ru/articles/SH-A-124
*/ 
// enum, cовместимый с #[repr(C)] enum (обязательно "старый стиль", а не enum class)
typedef enum {
    MOD = 0,    // 0/1  = off/on
    STA,        // device state
                //      0 Not displayed 
                //      1 Normal 
                //      2 Sleeping 
                //      3 Leaving home 
                //      4 Instant fragrance 
                //      5 Lack of essential oil 
                //      6 Cleaning
    ARA,        // area
    WLP,        // oil level
    ALP,        // aroma power
    STS,        // sleep_time_start
    STE,        // sleep_time_end
    SCN,	    // scenario index
    FSC,        // Instant fragrance mode switch
    FSM,        // fast smell mode
    FST,        // fast smell time
    FSS,        // fast smell start time
    FSR,        // fast smell repeat mask
    WTC,        // work_time_cd
    STC,        // stay_time_cd
    WCN,        // current work duration
    CLR,        // clear statistics
    CLN,        // clean on/off
    SLP,        // energy modal
    FFV,        // firmware version
    FFU,        // firmware update start
    FFS,        // firmware update status
    BCT,        // broadcast full update
    ACTORS_QTY,
} actor_t;



typedef enum {
    FFS_UNKNOWN,
    FFS_OK = 1,
    FFS_FAILED = 2,
} fungene_ota_status_t;



/**
 * @brief Возвращает C-строку с названием актора.
 * @param act - значение из actor_t
 * @return const char* (null-terminated string, пожизненно валидный, не требует free()).
 */
const char *actor_code_to_string(actor_t act);




/**
 * @brief Returns actor id from actor_t enum.
 * @param actor - pointer to char[3]! actor (not null terminated string)
 */
actor_t get_actor_id(const char *actor);


// TODO
// void set_actor(bsh_sys::mqtt_protocol::mqtt_in_block_t *block, actor_t actor);



// TODO
/**
 * @brief Check actor block value and its type for correctness
 *          Device - specific
 *          // TODO not implemented for now, always returns ok
 */ 
// bsh_sys::mqtt_protocol::pckt_integrity_err_events_t check_value (bsh_sys::mqtt_protocol::mqtt_in_block_t *cmd);





// } // extern "C"



// --- C++ sugar (inline helper) ---
inline std::string_view actor_name(actor_t act) {
    return actor_code_to_string(act);
}



} // namespace a630::mqtt_protocol