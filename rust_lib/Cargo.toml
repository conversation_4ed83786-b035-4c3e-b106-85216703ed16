[package]
name = "a630_rust_lib"
version = "0.1.0"
edition = "2021"

[lib]
name = "a630_rust_lib"
crate-type = ["staticlib", "rlib"]  # rlib нужен для тестов

[features]
default = ["esp-idf-sys"]
test = [] # Включает моки esp-idf-sys для тестов


[dependencies]
time = { version = "0.3", features = ["parsing", "formatting", "macros"] }
once_cell = "1.20.0"
lazy_static = "1.4.0"
esp-idf-sys = { version = "0.36.1", optional = true }

[profile.release]
opt-level = "s"
debug = false
# ! С lto = true какие то конфликты с embed-bitcode
# lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
debug = true
opt-level = 1
