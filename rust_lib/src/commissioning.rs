use std::sync::RwLock;



#[cfg(feature = "test")]
use crate::_esp_idf_sys_mocks::*;

#[cfg(feature = "esp-idf-sys")]
use esp_idf_sys::{
    esp_fill_random, nvs_handle_t, nvs_open, nvs_close,
    nvs_get_u16, nvs_set_u16, nvs_get_blob, nvs_set_blob, nvs_commit,
    ESP_OK, ESP_ERR_NVS_NOT_FOUND, ESP_ERR_NVS_INVALID_NAME,
    nvs_open_mode_t_NVS_READONLY as NVS_READONLY,
    nvs_open_mode_t_NVS_READWRITE as NVS_READWRITE,
};



/// Размер device ID (4 байта random + 6 байт MAC)
pub const DEVICE_ID_SIZE: usize = 10;
/// Размер GUID строки
pub const GUID_SIZE: usize = 54;
/// Максимальная длина параметра в NVS
pub const MAX_PARAMETER_LEN: usize = 200;   


/// Структура для хранения commissioning данных
#[derive(Debug, Clone)]
pub struct CommissioningData {
    pub wifi_ssid:      Option<String>,
    pub wifi_password:  Option<String>,
    pub mqtt_user_id:   Option<String>,
    pub mqtt_password:  Option<String>,
    pub balancer_link:  Option<String>,
    pub device_id:      Option<[u8; DEVICE_ID_SIZE]>,
    pub guid:           Option<String>,
    pub mac_address:    Option<[u8; 6]>,
}

/// Глобальное хранилище commissioning данных
static COMMISSIONING_DATA: RwLock<CommissioningData> = RwLock::new(CommissioningData {
    wifi_ssid:      None,
    wifi_password:  None,
    mqtt_user_id:   None,
    mqtt_password:  None,
    balancer_link:  None,
    device_id:      None,
    guid:           None,
    mac_address:    None,
});



/// Инициализация commissioning модуля
///
/// # Arguments
/// * `mac_address` - MAC адрес устройства (6 байт)
pub fn init(mac_address: [u8; 6]) -> Result<(), Box<dyn std::error::Error>> {
    COMMISSIONING_DATA.write().unwrap().mac_address = Some(mac_address);
    load_credentials()?;
    load_device_id()?;
    Ok(())
}

/// Установка WiFi SSID
pub fn set_wifi_ssid(ssid: &str) -> Result<(), Box<dyn std::error::Error>> {
    if ssid.len() > MAX_PARAMETER_LEN {
        return Err("SSID too long".into());
    }
    {
        let mut data = COMMISSIONING_DATA.write().unwrap();
        data.wifi_ssid = Some(ssid.to_string());
    }
    save_parameter("sys_cfg", "ssid", ssid, "ssid_l")?;
    Ok(())
}

/// Установка WiFi пароля
pub fn set_wifi_password(password: &str) -> Result<(), Box<dyn std::error::Error>> {
    if password.len() > MAX_PARAMETER_LEN {
        return Err("WiFi password too long".into());
    }
    {
        let mut data = COMMISSIONING_DATA.write().unwrap();
        data.wifi_password = Some(password.to_string());
    }
    save_parameter("sys_cfg", "wifi_ps", password, "wifi_p_l")?;
    Ok(())
}

/// Получение WiFi SSID
pub fn get_wifi_ssid() -> Option<String> {
    COMMISSIONING_DATA.read().unwrap().wifi_ssid.clone()
}

/// Получение WiFi пароля
pub fn get_wifi_password() -> Option<String> {
    COMMISSIONING_DATA.read().unwrap().wifi_password.clone()
}

/// Установка MQTT user ID
pub fn set_mqtt_user_id(user_id: &str) -> Result<(), Box<dyn std::error::Error>> {
    if user_id.len() > MAX_PARAMETER_LEN {
        return Err("MQTT user ID too long".into());
    }
    {
        let mut data = COMMISSIONING_DATA.write().unwrap();
        data.mqtt_user_id = Some(user_id.to_string());
    }
    save_parameter("sys_cfg", "uid", user_id, "uid_l")?;
    Ok(())
}

/// Установка MQTT пароля
pub fn set_mqtt_password(password: &str) -> Result<(), Box<dyn std::error::Error>> {
    if password.len() > MAX_PARAMETER_LEN {
        return Err("MQTT password too long".into());
    }
    {
        let mut data = COMMISSIONING_DATA.write().unwrap();
        data.mqtt_password = Some(password.to_string());
    }
    save_parameter("sys_cfg", "mqtt_passw", password, "mqtt_p_l")?;
    Ok(())
}

/// Получение MQTT user ID
pub fn get_mqtt_user_id() -> Option<String> {
    COMMISSIONING_DATA.read().unwrap().mqtt_user_id.clone()
}

/// Получение MQTT пароля
pub fn get_mqtt_password() -> Option<String> {
    COMMISSIONING_DATA.read().unwrap().mqtt_password.clone()
}

/// Установка balancer link
pub fn set_balancer_link(link: &str) -> Result<(), Box<dyn std::error::Error>> {
    if link.len() > MAX_PARAMETER_LEN {
        return Err("Balancer link too long".into());
    }
    {
        let mut data = COMMISSIONING_DATA.write().unwrap();
        data.balancer_link = Some(link.to_string());
    }
    save_parameter("sys_cfg", "blc_link", link, "blc_l_l")?;
    Ok(())
}

/// Получение balancer link
pub fn get_balancer_link() -> Option<String> {
    COMMISSIONING_DATA.read().unwrap().balancer_link.clone()
}



/********************************
    Device ID
*********************************/

/// Получение device ID как строки (20 символов hex)
pub fn get_device_id_as_string() -> String {
    let device_id = COMMISSIONING_DATA.read().unwrap().device_id.clone();
    if let Some(id) = device_id {
        id.iter()
            .map(|byte| format!("{:02x}", byte))
            .collect::<String>()
    } else {
        "0000000000000000000000".to_string()
    }
}

/// Получение device ID как массива байт
pub fn get_device_id() -> [u8; DEVICE_ID_SIZE] {
    let device_id = COMMISSIONING_DATA.read().unwrap().device_id.clone();
    device_id.unwrap_or([0u8; DEVICE_ID_SIZE])
}

/// Запись device ID в буфер
pub fn write_device_id_to_buffer(buffer: &mut [u8]) {
    if buffer.len() < DEVICE_ID_SIZE {
        return;
    }
    let device_id = get_device_id();
    buffer[..DEVICE_ID_SIZE].copy_from_slice(&device_id);
}



/// Запись device ID в буфер как строки (20 hex символов + null terminator)
pub fn write_device_id_to_buffer_as_string(buffer: &mut [u8]) -> usize {
    if buffer.len() < (DEVICE_ID_SIZE * 2 + 1) {
        return 0;
    }

    let device_id = get_device_id();
    let mut pos = 0;

    for byte in device_id.iter() {
        let hex_str = format!("{:02x}", byte);
        let hex_bytes = hex_str.as_bytes();
        buffer[pos..pos + 2].copy_from_slice(hex_bytes);
        pos += 2;
    }

    buffer[pos] = 0; // null terminator
    DEVICE_ID_SIZE * 2
}



/// Генерация GUID в формате bork_device_xxxx_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
pub fn generate_guid(guid_prefix: &str, buffer: &mut [u8]) -> Result<(), Box<dyn std::error::Error>> {
    if buffer.len() < GUID_SIZE {
        return Err("Buffer too small for GUID".into());
    }

    let prefix_bytes = guid_prefix.as_bytes();
    let prefix_len = prefix_bytes.len();

    if prefix_len >= GUID_SIZE {
        return Err("GUID prefix too long".into());
    }

    // Копируем prefix
    buffer[..prefix_len].copy_from_slice(prefix_bytes);

    // Генерируем случайную часть
    let random_size = GUID_SIZE - prefix_len - 1; // -1 для null terminator
    let mut random_bytes = vec![0u8; random_size / 2]; // каждый байт даст 2 hex символа

    unsafe {
        esp_fill_random(random_bytes.as_mut_ptr() as *mut _, random_bytes.len());
    }

    // Конвертируем в hex строку
    let mut pos = prefix_len;
    for (_i, &byte) in random_bytes.iter().enumerate() {
        let hex_str = format!("{:02x}", byte);
        let hex_bytes = hex_str.as_bytes();

        if pos + 2 <= GUID_SIZE - 1 {
            buffer[pos..pos + 2].copy_from_slice(hex_bytes);
            pos += 2;
        }

        // Добавляем дефисы в нужных местах (как в оригинальном коде)
        if pos < GUID_SIZE - 1 {
            match pos - prefix_len {
                8 | 13 | 18 | 23 => {
                    buffer[pos] = b'-';
                    pos += 1;
                }
                _ => {}
            }
        }
    }

    // Null terminator
    if pos < GUID_SIZE {
        buffer[pos] = 0;
    } else {
        buffer[GUID_SIZE - 1] = 0;
    }

    Ok(())
}



/// Получение GUID
pub fn get_guid(guid_prefix: &str) -> Result<String, Box<dyn std::error::Error>> {
    // Проверяем кеш
    {
        if let Some(guid) = &COMMISSIONING_DATA.read().unwrap().guid {
            return Ok(guid.clone());
        }
    }

    // Генерируем новый GUID
    let mut buffer = vec![0u8; GUID_SIZE];
    generate_guid(guid_prefix, &mut buffer)?;

    // Находим null terminator
    let end_pos = buffer.iter().position(|&x| x == 0).unwrap_or(GUID_SIZE - 1);
    let guid_string = String::from_utf8(buffer[..end_pos].to_vec())?;

    COMMISSIONING_DATA.write().unwrap().guid = Some(guid_string.clone());

    Ok(guid_string)
}



/********************************
         Локальные функции
*********************************/

/// Загрузка всех credentials из NVS
fn load_credentials() -> Result<(), Box<dyn std::error::Error>> {
    let mut data = COMMISSIONING_DATA.write().unwrap();

    // Загрузка WiFi SSID
    data.wifi_ssid = load_string_parameter("sys_cfg", "ssid", "ssid_l")?;

    // Загрузка WiFi пароля
    data.wifi_password = load_string_parameter("sys_cfg", "wifi_ps", "wifi_p_l")?;

    // Загрузка balancer link
    data.balancer_link = load_string_parameter("sys_cfg", "blc_link", "blc_l_l")?;

    // Загрузка MQTT user ID
    data.mqtt_user_id = load_string_parameter("sys_cfg", "uid", "uid_l")?;

    // Загрузка MQTT пароля
    data.mqtt_password = load_string_parameter("sys_cfg", "mqtt_passw", "mqtt_p_l")?;

    Ok(())
}



/// Загрузка строкового параметра из NVS
fn load_string_parameter(
    namespace: &str,
    param_name: &str,
    length_name: &str,
) -> Result<Option<String>, Box<dyn std::error::Error>> {
    let mut handle: nvs_handle_t = 0;

    // Открываем NVS
    let namespace_cstr = std::ffi::CString::new(namespace)?;
    let err = unsafe { nvs_open(namespace_cstr.as_ptr(), NVS_READONLY, &mut handle) };

    if err == ESP_ERR_NVS_NOT_FOUND {
        return Ok(None);
    }

    if err != ESP_OK {
        return Err("Failed to open NVS".into());
    }

    // Получаем длину
    let length_cstr = std::ffi::CString::new(length_name)?;
    let mut length: u16 = 0;
    let err = unsafe { nvs_get_u16(handle, length_cstr.as_ptr(), &mut length) };

    if err != ESP_OK {
        unsafe { nvs_close(handle) };
        return Ok(None);
    }

    // Получаем данные
    let param_cstr = std::ffi::CString::new(param_name)?;
    let mut buffer = vec![0u8; length as usize];
    let mut actual_length = length as usize;
    let err = unsafe {
        nvs_get_blob(handle, param_cstr.as_ptr(), buffer.as_mut_ptr() as *mut _, &mut actual_length)
    };

    unsafe { nvs_close(handle) };

    if err != ESP_OK {
        return Ok(None);
    }

    // Убираем null terminator если есть
    if let Some(pos) = buffer.iter().position(|&x| x == 0) {
        buffer.truncate(pos);
    }

    Ok(Some(String::from_utf8(buffer)?))
}

/// Сохранение параметра в NVS
fn save_parameter(
    namespace: &str,
    param_name: &str,
    value: &str,
    length_name: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut handle: nvs_handle_t = 0;

    // Открываем NVS
    let namespace_cstr = std::ffi::CString::new(namespace)?;
    let err = unsafe { nvs_open(namespace_cstr.as_ptr(), NVS_READWRITE, &mut handle) };

    if err != ESP_OK {
        return Err("Failed to open NVS for write".into());
    }

    let value_bytes = value.as_bytes();
    let length = (value_bytes.len() + 1) as u16; // +1 для null terminator

    // Сохраняем длину
    let length_cstr = std::ffi::CString::new(length_name)?;
    let err = unsafe { nvs_set_u16(handle, length_cstr.as_ptr(), length) };

    if err != ESP_OK {
        unsafe { nvs_close(handle) };
        return Err("Failed to save length".into());
    }

    // Сохраняем данные с null terminator
    let mut data_with_null = value_bytes.to_vec();
    data_with_null.push(0);

    let param_cstr = std::ffi::CString::new(param_name)?;
    let err = unsafe {
        nvs_set_blob(handle, param_cstr.as_ptr(), data_with_null.as_ptr() as *const _, data_with_null.len())
    };

    if err != ESP_OK {
        unsafe { nvs_close(handle) };
        return Err("Failed to save parameter".into());
    }

    // Коммитим изменения
    let err = unsafe { nvs_commit(handle) };
    unsafe { nvs_close(handle) };

    if err != ESP_OK {
        return Err("Failed to commit NVS".into());
    }

    Ok(())
}

/// Загрузка device ID
fn load_device_id() -> Result<(), Box<dyn std::error::Error>> {
    let mut handle: nvs_handle_t = 0;

    // Открываем NVS
    let namespace_cstr = std::ffi::CString::new("sys_cfg")?;
    let err = unsafe { nvs_open(namespace_cstr.as_ptr(), NVS_READWRITE, &mut handle) };

    if err != ESP_OK {
        return Err("Failed to open NVS".into());
    }

    let mut buffer = [0u8; DEVICE_ID_SIZE];
    let mut actual_length = DEVICE_ID_SIZE;
    let param_cstr = std::ffi::CString::new("dev_ID")?;
    let err = unsafe {
        nvs_get_blob(handle, param_cstr.as_ptr(), buffer.as_mut_ptr() as *mut _, &mut actual_length)
    };

    if err == ESP_OK {
        // ID найден, сохраняем его
        COMMISSIONING_DATA.write().unwrap().device_id = Some(buffer);
        unsafe { nvs_close(handle) };
    } else if err == ESP_ERR_NVS_NOT_FOUND || err == ESP_ERR_NVS_INVALID_NAME {
        // ID не найден, генерируем новый
        generate_and_save_device_id(handle)?;
        unsafe { nvs_close(handle) };
    } else {
        unsafe { nvs_close(handle) };
        return Err("Failed to load device ID".into());
    }

    Ok(())
}

/// Генерация и сохранение нового device ID
fn generate_and_save_device_id(handle: nvs_handle_t) -> Result<(), Box<dyn std::error::Error>> {
    let mut device_id = [0u8; DEVICE_ID_SIZE];

    // Генерируем 4 случайных байта
    let mut random_part = [0u8; 4];
    unsafe {
        esp_fill_random(random_part.as_mut_ptr() as *mut _, 4);
    }

    // Проверяем, что все байты разные (как в оригинальном коде)
    let mut counter = 0;
    while counter < 10 {
        let mut all_different = true;
        for i in 1..4 {
            if random_part[0] == random_part[i] {
                all_different = false;
                break;
            }
        }
        if all_different {
            break;
        }
        unsafe {
            esp_fill_random(random_part.as_mut_ptr() as *mut _, 4);
        }
        counter += 1;
    }

    // Получаем MAC адрес из глобального хранилища
    let mac_address = COMMISSIONING_DATA.read().unwrap().mac_address;
    if let Some(mac) = mac_address {
        device_id[..4].copy_from_slice(&random_part);
        device_id[4..].copy_from_slice(&mac);
    } else {
        return Err("MAC address not initialized".into());
    }
    
    // Сохраняем в NVS
    let param_cstr = std::ffi::CString::new("dev_ID")?;
    let err = unsafe {
        nvs_set_blob(handle, param_cstr.as_ptr(), device_id.as_ptr() as *const _, DEVICE_ID_SIZE)
    };

    if err != ESP_OK {
        return Err("Failed to save device ID".into());
    }

    // Коммитим изменения
    let err = unsafe { nvs_commit(handle) };
    if err != ESP_OK {
        return Err("Failed to commit device ID".into());
    }

    // Сохраняем в глобальной переменной
    COMMISSIONING_DATA.write().unwrap().device_id = Some(device_id);

    Ok(())
}
