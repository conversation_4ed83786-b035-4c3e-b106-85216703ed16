//! Mock implementations of ESP-IDF system functions for testing
//!
//! This module provides mock implementations of ESP-IDF functions that can be used
//! during testing when the actual ESP-IDF is not available. The mocks simulate
//! the behavior of the real functions but store data in memory instead of flash.
#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

// #[cfg(test)]
// pub mod mocks {

use std::collections::HashMap;
use std::sync::Mutex;
use std::ffi::{CStr};
use std::os::raw::{c_char, c_void, c_int};
use once_cell::sync::Lazy;

#[cfg(feature = "test")]
use std::sync::atomic::{AtomicI64, Ordering};



// ESP-IDF error codes
pub type esp_err_t = c_int;
pub const ESP_OK: c_int = 0;
pub const ESP_ERR_NVS_NOT_FOUND: c_int = 0x1106;
pub const ESP_ERR_NVS_INVALID_NAME: c_int = 0x1108;
pub const ESP_ERR_INVALID_ARG: c_int = 0x102;
pub const ESP_ERR_NOT_FOUND: c_int = 0x105;

// NVS open modes
pub const NVS_READONLY: c_int = 1;
pub const NVS_READWRITE: c_int = 2;

// NVS open mode constants with full names as requested
pub const nvs_open_mode_t_NVS_READONLY: c_int = NVS_READONLY;
pub const nvs_open_mode_t_NVS_READWRITE: c_int = NVS_READWRITE;

// Type definitions
pub type nvs_handle_t = u32;

// Global storage for NVS data
static NVS_STORAGE: Lazy<Mutex<HashMap<String, HashMap<String, Vec<u8>>>>> = 
    Lazy::new(|| Mutex::new(HashMap::new()));
static mut HANDLE_COUNTER: nvs_handle_t = 1;
static HANDLE_MAP: Lazy<Mutex<HashMap<nvs_handle_t, (String, c_int)>>> = Lazy::new(|| {
    Mutex::new(HashMap::new())
});

/// Fill a buffer with random bytes
#[no_mangle]
pub unsafe fn esp_fill_random(buf: *mut c_void, len: usize) {
    if buf.is_null() || len == 0 {
        return;
    }

    let buffer = unsafe { std::slice::from_raw_parts_mut(buf as *mut u8, len) };

    // Fill with pseudo-random data for testing
    for (i, byte) in buffer.iter_mut().enumerate() {
        *byte = ((i * 17 + 42) % 256) as u8;
    }
}


#[cfg(feature = "test")]
/// Get current time in microseconds since boot
#[no_mangle]
pub unsafe fn esp_timer_get_time() -> i64 {
    let mock = MOCK_TIME.load(Ordering::Relaxed);
    if mock != 0 {

        return mock;
    }

    // Если мок не установлен, возвращается реальное системное время
    return 0;
}





/// Open NVS namespace
#[no_mangle]
pub unsafe fn nvs_open(
    namespace_name: *const c_char,
    open_mode: c_int,
    out_handle: *mut nvs_handle_t,
) -> c_int {
    if namespace_name.is_null() || out_handle.is_null() {
        return ESP_ERR_NVS_INVALID_NAME;
    }

    let namespace = unsafe {
        match CStr::from_ptr(namespace_name).to_str() {
            Ok(s) => s.to_string(),
            Err(_) => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    if namespace.is_empty() || namespace.len() > 15 {
        return ESP_ERR_NVS_INVALID_NAME;
    }

    // Create namespace if it doesn't exist and mode is readwrite
    {
        let mut storage = NVS_STORAGE.lock().unwrap();
        if !storage.contains_key(&namespace) {
            if open_mode == NVS_READONLY {
                return ESP_ERR_NVS_NOT_FOUND;
            }
            storage.insert(namespace.clone(), HashMap::new());
        }
    }

    // Generate handle
    let handle = unsafe {
        HANDLE_COUNTER += 1;
        HANDLE_COUNTER
    };

    // Store handle mapping
    {
        let mut handle_map = HANDLE_MAP.lock().unwrap();
        handle_map.insert(handle, (namespace, open_mode));
    }

    unsafe {
        *out_handle = handle;
    }

    ESP_OK
}

/// Close NVS handle
#[no_mangle]
pub unsafe fn nvs_close(handle: nvs_handle_t) {
    let mut handle_map = HANDLE_MAP.lock().unwrap();
    handle_map.remove(&handle);
}

/// Get u16 value from NVS
#[no_mangle]
pub unsafe fn nvs_get_u16(
    handle: nvs_handle_t,
    key: *const c_char,
    out_value: *mut u16,
) -> c_int {
    if key.is_null() || out_value.is_null() {
        return ESP_ERR_NVS_INVALID_NAME;
    }

    let key_str = unsafe {
        match CStr::from_ptr(key).to_str() {
            Ok(s) => s.to_string(),
            Err(_) => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    let (namespace, _) = {
        let handle_map = HANDLE_MAP.lock().unwrap();
        match handle_map.get(&handle) {
            Some(info) => info.clone(),
            None => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    let storage = NVS_STORAGE.lock().unwrap();
    let namespace_data = match storage.get(&namespace) {
        Some(data) => data,
        None => return ESP_ERR_NVS_NOT_FOUND,
    };

    match namespace_data.get(&key_str) {
        Some(data) if data.len() >= 2 => {
            let value = u16::from_le_bytes([data[0], data[1]]);
            unsafe {
                *out_value = value;
            }
            ESP_OK
        }
        _ => ESP_ERR_NVS_NOT_FOUND,
    }
}

/// Set u16 value in NVS
#[no_mangle]
pub unsafe fn nvs_set_u16(
    handle: nvs_handle_t,
    key: *const c_char,
    value: u16,
) -> c_int {
    if key.is_null() {
        return ESP_ERR_NVS_INVALID_NAME;
    }

    let key_str = unsafe {
        match CStr::from_ptr(key).to_str() {
            Ok(s) => s.to_string(),
            Err(_) => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    let (namespace, open_mode) = {
        let handle_map = HANDLE_MAP.lock().unwrap();
        match handle_map.get(&handle) {
            Some(info) => info.clone(),
            None => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    if open_mode == NVS_READONLY {
        return ESP_ERR_NVS_INVALID_NAME; // Should be read-only error, but using this for simplicity
    }

    let mut storage = NVS_STORAGE.lock().unwrap();
    let namespace_data = storage.get_mut(&namespace).unwrap();

    let bytes = value.to_le_bytes().to_vec();
    namespace_data.insert(key_str, bytes);

    ESP_OK
}

/// Get blob data from NVS
#[no_mangle]
pub unsafe fn nvs_get_blob(
    handle: nvs_handle_t,
    key: *const c_char,
    out_value: *mut c_void,
    length: *mut usize,
) -> c_int {
    if key.is_null() || length.is_null() {
        return ESP_ERR_NVS_INVALID_NAME;
    }

    let key_str = unsafe {
        match CStr::from_ptr(key).to_str() {
            Ok(s) => s.to_string(),
            Err(_) => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    let (namespace, _) = {
        let handle_map = HANDLE_MAP.lock().unwrap();
        match handle_map.get(&handle) {
            Some(info) => info.clone(),
            None => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    let storage = NVS_STORAGE.lock().unwrap();
    let namespace_data = match storage.get(&namespace) {
        Some(data) => data,
        None => return ESP_ERR_NVS_NOT_FOUND,
    };

    match namespace_data.get(&key_str) {
        Some(data) => {
            let required_length = data.len();
            let provided_length = unsafe { *length };

            unsafe {
                *length = required_length;
            }

            if out_value.is_null() {
                // Just return the required length
                return ESP_OK;
            }

            if provided_length < required_length {
                return ESP_ERR_NVS_INVALID_NAME; // Should be buffer too small error
            }

            unsafe {
                std::ptr::copy_nonoverlapping(
                    data.as_ptr(),
                    out_value as *mut u8,
                    required_length,
                );
            }

            ESP_OK
        }
        None => ESP_ERR_NVS_NOT_FOUND,
    }
}

/// Set blob data in NVS
#[no_mangle]
pub unsafe fn nvs_set_blob(
    handle: nvs_handle_t,
    key: *const c_char,
    value: *const c_void,
    length: usize,
) -> c_int {
    if key.is_null() || (value.is_null() && length > 0) {
        return ESP_ERR_NVS_INVALID_NAME;
    }

    let key_str = unsafe {
        match CStr::from_ptr(key).to_str() {
            Ok(s) => s.to_string(),
            Err(_) => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    let (namespace, open_mode) = {
        let handle_map = HANDLE_MAP.lock().unwrap();
        match handle_map.get(&handle) {
            Some(info) => info.clone(),
            None => return ESP_ERR_NVS_INVALID_NAME,
        }
    };

    if open_mode == NVS_READONLY {
        return ESP_ERR_NVS_INVALID_NAME; // Should be read-only error
    }

    let data = if length == 0 {
        Vec::new()
    } else {
        unsafe {
            std::slice::from_raw_parts(value as *const u8, length).to_vec()
        }
    };

    let mut storage = NVS_STORAGE.lock().unwrap();
    let namespace_data = storage.get_mut(&namespace).unwrap();
    namespace_data.insert(key_str, data);

    ESP_OK
}

/// Commit changes to NVS
#[no_mangle]
pub unsafe fn nvs_commit(handle: nvs_handle_t) -> c_int {
    let handle_map = HANDLE_MAP.lock().unwrap();
    match handle_map.get(&handle) {
        Some(_) => ESP_OK,
        None => ESP_ERR_NVS_INVALID_NAME,
    }
}


/// Глобальная переменная для мокового времени (0 = не используется)
#[cfg(feature = "test")]
static MOCK_TIME: AtomicI64 = AtomicI64::new(0);

#[cfg(feature = "test")]
pub fn set_esp_timer_mock(time: i64) {
    MOCK_TIME.store(time, Ordering::Relaxed);
}

#[cfg(feature = "test")]
pub fn clear_esp_timer_mock() {
    MOCK_TIME.store(0, Ordering::Relaxed);
}
// }   // end of cfg(test)