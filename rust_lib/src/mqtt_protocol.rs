#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Eq)]
pub struct Actor {
    pub id: u32,
    pub mnemocode: &'static [u8; 3], // фиксированные 3 байта
}

pub static ACTORS: &[Actor] = &[
    Actor { id: 0, mnemocode: b"MOD" },
    Actor { id: 1, mnemocode: b"STA" },
    Actor { id: 2, mnemocode: b"ARA" },
    Actor { id: 3, mnemocode: b"WLP" },
    Actor { id: 4, mnemocode: b"ALP" },
    Actor { id: 5, mnemocode: b"STS" },
    Actor { id: 6, mnemocode: b"STE" },
    Actor { id: 7, mnemocode: b"SCN" },
    Actor { id: 8, mnemocode: b"FSC" },
    Actor { id: 9, mnemocode: b"FSM" },
    Actor { id: 10, mnemocode: b"FST" },
    Actor { id: 11, mnemocode: b"FSS" },
    Actor { id: 12, mnemocode: b"FSR" },
    Actor { id: 13, mnemocode: b"WTC" },
    Actor { id: 14, mnemocode: b"STC" },
    Actor { id: 15, mnemocode: b"WCN" },
    Actor { id: 16, mnemocode: b"CLR" },
    Actor { id: 17, mnemocode: b"CLN" },
    Actor { id: 18, mnemocode: b"SLP" },
    Actor { id: 19, mnemocode: b"FFV" },
    Actor { id: 20, mnemocode: b"FFU" },
    Actor { id: 21, mnemocode: b"FFS" },
    Actor { id: 22, mnemocode: b"BCT" },
];

pub fn get_by_id(id: u32) -> Option<&'static Actor> {
    ACTORS.iter().find(|a| a.id == id)
}

pub fn get_by_code(code: &[u8; 3]) -> Option<&'static Actor> {
    ACTORS.iter().find(|a| a.mnemocode == code)
}
