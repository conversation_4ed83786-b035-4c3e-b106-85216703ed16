#!/bin/bash

# Скрипт для сборки Rust библиотеки в Docker контейнере
# Использует официальный образ espressif/idf-rust

echo "=== Сборка Rust библиотеки в Docker (официальный образ Espressif) ==="

if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

mkdir -p .cargo_cache

DOCKER_IMAGE="espressif/idf-rust:esp32s3_1.88.0.0"
echo "Используется Docker образ: $DOCKER_IMAGE"

docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/opt/rust/cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    --env CARGO_HOME=/opt/rust/cargo \
    --env RUST_BACKTRACE=1 \
    $DOCKER_IMAGE /bin/bash -c "
        echo ''
        echo 'Информация о среде сборки:'
        rustc --version
        cargo --version

        echo ''
        echo 'Информация о toolchain:'
        rustup show

        echo ''
        echo 'Переменные окружения ESP:'
        env | grep -E '(ESP|IDF|RUST)' | sort || echo 'ESP переменные не найдены'

        echo ''
        echo 'Проверка синтаксиса Rust кода...'
        if ! cargo check --color always --target=xtensa-esp32s3-espidf -Zbuild-std=std,panic_abort; then
            echo '✗ Ошибка при проверке синтаксиса Rust кода'
            exit 1
        fi

        echo ''
        echo 'Сборка Rust библиотеки для ESP32-S3...'
        if ! cargo build --release --color always --target=xtensa-esp32s3-espidf -Zbuild-std=std,panic_abort; then
            echo '✗ Ошибка при сборке Rust библиотеки'
            exit 1
        fi

        echo ''
        if [ -f target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a ]; then
            ls -la target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
            echo 'Размер библиотеки:'
            du -h target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
            echo '✓ Rust библиотека успешно собрана'
        else
            echo '✗ Ошибка: библиотека не найдена после успешной сборки'
            echo 'Содержимое target директории:'
            find target -name '*.a' -o -name '*.rlib' | head -10
            exit 1
        fi
    "