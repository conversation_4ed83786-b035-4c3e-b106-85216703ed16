#!/usr/bin/env bash
set -euo pipefail

DOCKER_IMAGE="rust:1.81.0"
echo "Используется Docker образ: $DOCKER_IMAGE"

# локальная папка для кеша зависимостей Cargo
CARGO_CACHE_DIR="$HOME/.cargo-docker"
mkdir -p "$CARGO_CACHE_DIR"

docker run --rm \
    -e CARGO_HOME=/cargo \
    -e CARGO_TARGET_DIR=/workspace/target/linux \
    -v "$PWD":/workspace \
    -v "$CARGO_CACHE_DIR":/cargo \
    -v target-cache:/workspace/target \
    -w /workspace/rust_lib \
    "$DOCKER_IMAGE" \
    /bin/bash -c "cargo test --no-default-features --features test --color always"