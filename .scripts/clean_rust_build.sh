#!/bin/bash

# Скрипт для очистки Rust артефактов сборки

echo "=== Очистка Rust артефактов ==="

# Очистка Rust target директории
if [ -d "rust_lib/target" ]; then
    echo "Удаление rust_lib/target..."
    rm -rf rust_lib/target
    echo "✓ rust_lib/target удалена"
else
    echo "rust_lib/target не найдена"
fi

# Очистка Cargo кэша - опционально
read -p "Очистить кэш Cargo? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -d ".cargo_cache" ]; then
        echo "Удаление .cargo_cache..."
        rm -rf .cargo_cache
        echo "✓ .cargo_cache удалена"
    else
        echo ".cargo_cache не найдена"
    fi
fi

echo "=== Очистка Rust артефактов завершена ==="
