СХЕМА ЗАВИСИМОСТЕЙ BSH СИСТЕМЫ (main/_BSH_system/)
===================================================

Легенда:
--------
{модуль} - BSH модуль
---> - обычная зависимость (include заголовочного файла)
===> - сильная зависимость (активное использование функций)
<--> - взаимная зависимость (перекрестная)
[!] - потенциальная кольцевая зависимость

ЦЕНТРАЛЬНЫЙ МОДУЛЬ:
==================

{system} - ГЛАВНЫЙ КООРДИНАТОР
    |
    +---> {system_internal}
    +---> {mqtt_helpers}
    +---> {BLE/bluetooth_LE}
    +---> {RSA_encryption/rsa_encryption}
    +---> {device_id/device_id}
    +---> {commissioning/commissioning}
    +---> {mqtt_logs/mqtt_logs}
    +---> {BLE_logging/ble_logging}
    +---> {log_levels/logs_levels}
    |
    +===> {MQTT/mqtt}
    +===> {UART/uart}
    +===> {_WIFI/wifi}
    +===> {mqtt_sys_protocol/mqtt_sys_protocol}
    +===> {date_and_time/date_and_time}
    +===> {request_to_balancer/http_request_to_balancer}
    +===> {telemetry_storage/telemetry_storage}
    +===> {ble_comm/ble_comm_logic}
    +===> {_OTA/bsh_ota}


ДЕТАЛЬНЫЕ ВНУТРЕННИЕ ЗАВИСИМОСТИ:
=================================

{system_internal}
    ---> {_WIFI/wifi}
    ---> {request_to_balancer/http_request_to_balancer}

{commissioning/commissioning}
    ---> {device_id/device_id}
    ---> {mqtt_logs/mqtt_logs}

{ble_comm/ble_comm_logic} [!] МНОЖЕСТВЕННЫЕ ЗАВИСИМОСТИ
    ---> {system/system}                    ← ОБРАТНАЯ ЗАВИСИМОСТЬ!
    ---> {system_internal}
    ---> {_WIFI/wifi}
    ---> {MQTT/mqtt}
    ---> {RSA_encryption/rsa_encryption}
    ---> {BLE/bluetooth_LE}
    ---> {device_id/device_id}
    ---> {commissioning/commissioning}
    ---> {mqtt_sys_protocol/mqtt_sys_protocol}

{MQTT/mqtt}
    ---> {MQTT/reconnect_logic}

{mqtt_sys_protocol/mqtt_sys_protocol}
    ---> {MQTT/mqtt}
    ---> {RSA_encryption/rsa_encryption}
    ---> {BLE/bluetooth_LE}
    ---> {mqtt_logs/mqtt_logs}

{telemetry_storage/telemetry_storage}
    ---> {date_and_time/date_and_time}
    ---> {mqtt_logs/mqtt_logs}

{BLE/bluetooth_LE}
    ---> {BLE/ble_internal}
    ---> {BLE/DEVICE_BLE_CONFIG}
    ---> {system/system}                    ← ОБРАТНАЯ ЗАВИСИМОСТЬ!
    ---> {RSA_encryption/rsa_encryption}
    ---> {commissioning/commissioning}

{mqtt_logs/mqtt_logs}
    ---> {mqtt_logs/mqtt_logs_buffer}
    ---> {date_and_time/date_and_time}
    ---> {MQTT/mqtt}
    ---> {device_id/device_id}
    ---> {commissioning/commissioning}

{device_id/device_id}
    ---> {mqtt_logs/mqtt_logs}

{log_levels/logs_levels}
    ---> {log_levels/SYS_LOG_LEVELS}


ОБНАРУЖЕННЫЕ КОЛЬЦЕВЫЕ ЗАВИСИМОСТИ:
===================================

[!] КОЛЬЦО 1: system <--> ble_comm_logic <--> system
    {system/system} ===> {ble_comm/ble_comm_logic}
    {ble_comm/ble_comm_logic} ---> {system/system}

[!] КОЛЬЦО 2: system <--> BLE/bluetooth_LE <--> system  
    {system/system} ===> {BLE/bluetooth_LE}
    {BLE/bluetooth_LE} ---> {system/system}

[!] КОЛЬЦО 3: commissioning <--> mqtt_logs <--> commissioning
    {commissioning/commissioning} ---> {mqtt_logs/mqtt_logs}
    {mqtt_logs/mqtt_logs} ---> {commissioning/commissioning}

[!] КОЛЬЦО 4: device_id <--> mqtt_logs <--> device_id
    {device_id/device_id} ---> {mqtt_logs/mqtt_logs}
    {mqtt_logs/mqtt_logs} ---> {device_id/device_id}


ПЕРЕКРЕСТНЫЕ ЗАВИСИМОСТИ:
========================

{mqtt_logs/mqtt_logs} является ЦЕНТРАЛЬНЫМ УЗЛОМ логирования:
    ← {commissioning/commissioning}
    ← {device_id/device_id}
    ← {telemetry_storage/telemetry_storage}
    ← {mqtt_sys_protocol/mqtt_sys_protocol}
    → {MQTT/mqtt}
    → {date_and_time/date_and_time}
    → {commissioning/commissioning}  [КОЛЬЦО!]
    → {device_id/device_id}          [КОЛЬЦО!]

{RSA_encryption/rsa_encryption} используется многими модулями:
    ← {system/system}
    ← {ble_comm/ble_comm_logic}
    ← {mqtt_sys_protocol/mqtt_sys_protocol}
    ← {BLE/bluetooth_LE}

{commissioning/commissioning} имеет множественные связи:
    ← {system/system}
    ← {ble_comm/ble_comm_logic}
    ← {BLE/bluetooth_LE}
    → {device_id/device_id}
    → {mqtt_logs/mqtt_logs}
    ← {mqtt_logs/mqtt_logs}          [КОЛЬЦО!]


ИЕРАРХИЧЕСКИЕ УРОВНИ:
====================

УРОВЕНЬ 0 (Базовые утилиты):
    {log_levels/SYS_LOG_LEVELS}
    {BLE/DEVICE_BLE_CONFIG}
    {BLE/ble_internal}
    {MQTT/mqtt_out_obj}
    {mqtt_logs/mqtt_logs_buffer}

УРОВЕНЬ 1 (Основные сервисы):
    {log_levels/logs_levels}
    {date_and_time/date_and_time}
    {RSA_encryption/rsa_encryption}
    {_WIFI/wifi}
    {UART/uart}
    {MQTT/reconnect_logic}

УРОВЕНЬ 2 (Функциональные модули):
    {device_id/device_id}
    {MQTT/mqtt}
    {request_to_balancer/http_request_to_balancer}
    {_OTA/bsh_ota}

УРОВЕНЬ 3 (Интеграционные модули):
    {commissioning/commissioning}
    {mqtt_logs/mqtt_logs}
    {BLE/bluetooth_LE}
    {telemetry_storage/telemetry_storage}

УРОВЕНЬ 4 (Протокольные модули):
    {mqtt_sys_protocol/mqtt_sys_protocol}
    {BLE_logging/ble_logging}

УРОВЕНЬ 5 (Координационные модули):
    {system_internal}
    {mqtt_helpers}
    {ble_comm/ble_comm_logic}

УРОВЕНЬ 6 (Главный координатор):
    {system/system}


ПРОБЛЕМНЫЕ ОБЛАСТИ:
==================

1. МНОЖЕСТВЕННЫЕ ОБРАТНЫЕ СВЯЗИ:
   - {system} включает многие модули, но некоторые модули обращаются обратно к {system}
   - Это создает тесную связанность и затрудняет тестирование

2. ЦЕНТРАЛЬНЫЕ УЗЛЫ С МНОЖЕСТВЕННЫМИ СВЯЗЯМИ:
   - {mqtt_logs} используется почти всеми модулями
   - {RSA_encryption} используется многими модулями
   - {commissioning} имеет связи с множеством модулей

3. КОЛЬЦЕВЫЕ ЗАВИСИМОСТИ:
   - 4 обнаруженных кольца создают проблемы для:
     * Порядка инициализации модулей
     * Юнит-тестирования
     * Рефакторинга кода

4. НАРУШЕНИЕ ПРИНЦИПА ЕДИНСТВЕННОЙ ОТВЕТСТВЕННОСТИ:
   - {ble_comm_logic} знает слишком много о других модулях
   - {system} выполняет слишком много функций


РЕКОМЕНДАЦИИ:
============

1. РАЗОРВАТЬ КОЛЬЦЕВЫЕ ЗАВИСИМОСТИ:
   - Использовать паттерн Observer для уведомлений
   - Вынести общие интерфейсы в отдельные заголовочные файлы
   - Использовать dependency injection

2. УМЕНЬШИТЬ СВЯЗАННОСТЬ:
   - Создать абстрактные интерфейсы для основных сервисов
   - Использовать event-driven архитектуру
   - Разделить {system} на более мелкие специализированные модули

3. ЦЕНТРАЛИЗОВАТЬ ОБЩИЕ СЕРВИСЫ:
   - Создать единый logging framework
   - Создать единый configuration manager
   - Создать единый event dispatcher
