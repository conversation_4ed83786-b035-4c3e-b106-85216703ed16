СХЕМА ЗАВИСИМОСТЕЙ МОДУЛЕЙ ПРОЕКТА A630_AROMA
==============================================

Легенда:
--------
[Модуль] - основной модуль
{Модуль} - системный модуль BSH
<Модуль> - Rust модуль
---> - зависимость (включает заголовочный файл)
===> - сильная зависимость (основная функциональность)

ОСНОВНАЯ АРХИТЕКТУРА:
====================

                                [main.cpp]
                                    |
                                    v
                            [main_task/main_task]
                                    |
                    +---------------+---------------+
                    |               |               |
                    v               v               v
            [uart_protocol]  [mqtt_protocol]  [telemetry_filter]
                    |               |               |
                    |               v               |
                    |        {system/system} <-----+
                    |               |
                    +---------------+
                            |
                            v
                    [device_specific_data]


RUST БИБЛИОТЕКА:
================

<rust_lib/lib.rs>
    |
    +---> <canonical_rust.rs>
    +---> <ffi.rs>
    +---> <date_and_time_canonical.rs>
    +---> <date_and_time_ffi.rs>


BSH СИСТЕМА (main/_BSH_system/):
===============================

{system/system} - ЦЕНТРАЛЬНЫЙ МОДУЛЬ
    |
    +---> {system_internal}
    +---> {mqtt_helpers}
    +---> {BLE/bluetooth_LE}
    +---> {RSA_encryption/rsa_encryption}
    +---> {device_id/device_id}
    +---> {commissioning/commissioning}
    +---> {mqtt_logs/mqtt_logs}
    +---> {BLE_logging/ble_logging}
    +---> {log_levels/logs_levels}
    |
    +===> {MQTT/mqtt} ---------------+
    +===> {UART/uart}                |
    +===> {_WIFI/wifi}               |
    +===> {mqtt_sys_protocol}        |
    +===> {date_and_time}            |
    +===> {request_to_balancer}      |
    +===> {telemetry_storage}        |
    +===> {ble_comm/ble_comm_logic}  |
    +===> {_OTA/bsh_ota}             |
                                     |
                                     v
                            ВНЕШНИЕ ESP-IDF КОМПОНЕНТЫ


ДЕТАЛЬНЫЕ ЗАВИСИМОСТИ BSH МОДУЛЕЙ:
==================================

{system/system}
    ---> {mqtt_sys_protocol/mqtt_sys_protocol}
    ---> {telemetry_storage/telemetry_storage}
    ---> {UART/uart}
    ---> {_WIFI/wifi}
    ---> {request_to_balancer/http_request_to_balancer}
    ---> {date_and_time/date_and_time}
    ---> {BLE/bluetooth_LE}
    ---> {ble_comm/ble_comm_logic}
    ---> {_OTA/bsh_ota}

{BLE/bluetooth_LE}
    ---> {BLE/ble_internal}
    ---> {BLE/DEVICE_BLE_CONFIG}

{MQTT/mqtt}
    ---> {MQTT/mqtt_out_obj}
    ---> {MQTT/reconnect_logic}

{mqtt_logs/mqtt_logs}
    ---> {mqtt_logs/mqtt_logs_buffer}

{log_levels/logs_levels}
    ---> {log_levels/SYS_LOG_LEVELS}


ОСНОВНЫЕ МОДУЛИ ПРИЛОЖЕНИЯ:
==========================

[main_task/main_task]
    ---> [main_task/queues_helpers]
    ---> [uart_protocol/uart_protocol]
    ---> [telemetry_filter/telemetry_filter]
    ---> [device_specific_data]
    ---> {system/system}
    ---> [emulator/emulator]
    ---> {mqtt_logs/mqtt_logs}

[emulator/emulator]
    ---> [mqtt_protocol/mqtt_protocol]
    ---> [main_task/main_task]
    ---> [main_task/queues_helpers]

[uart_protocol/uart_protocol]
    ---> [device_specific_data]

[mqtt_protocol/mqtt_protocol]
    ---> {mqtt_sys_protocol/mqtt_sys_protocol}

[telemetry_filter/telemetry_filter]
    ---> [mqtt_protocol/mqtt_protocol]


ВНЕШНИЕ ЗАВИСИМОСТИ:
===================

ESP-IDF Framework:
    - freertos (FreeRTOS)
    - esp_system
    - esp_log
    - nvs_flash
    - esp_ota_ops
    - esp_timer
    - esp_event
    - driver (uart, gpio)
    - esp_wifi
    - esp_netif
    - mqtt_client
    - esp_http_client
    - esp_https_ota
    - mbedtls
    - esp_bt (Bluetooth)

Rust библиотека:
    - time crate (для работы с датой/временем)


ПОТОКИ ДАННЫХ:
==============

UART поток:
[uart_protocol] ---> [main_task] ---> [telemetry_filter] ---> [mqtt_protocol]

MQTT поток:
{MQTT/mqtt} ---> {mqtt_sys_protocol} ---> [main_task] ---> [emulator]

BLE поток:
{BLE/bluetooth_LE} ---> {ble_comm/ble_comm_logic} ---> {system/system}

Телеметрия:
[telemetry_filter] ---> {telemetry_storage} ---> {MQTT/mqtt}


ПРИМЕЧАНИЯ:
===========

1. {system/system} является центральным модулем, который инициализирует
   и координирует работу всех подсистем BSH.

2. Модули в _BSH_system/ представляют собой переиспользуемую библиотеку
   для устройств умного дома.

3. Rust библиотека предоставляет функции через FFI интерфейс для работы
   с датой/временем и другими утилитами.

4. Основной поток выполнения: main.cpp -> main_task -> обработка UART/MQTT

5. Эмулятор используется для тестирования без реального оборудования.
